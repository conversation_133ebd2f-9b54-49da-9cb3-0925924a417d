
Context# Golang Migration Plan for GNRS Backend (Django + FastAPI)

## Executive Summary
This document proposes a detailed plan to migrate the current Python backend — a FastAPI application paired with a Django-based authentication service — to Go (Golang) while preserving API compatibility, functionality, and operational reliability. The plan includes a thorough codebase analysis, Go architecture design, compatibility guarantees, migration and deployment strategy, testing and rollback procedures, phased timeline, and risk mitigation.

Context

## 1) Codebase Analysis (Current State)

### 1.1 Tech Stack and Structure
- Primary app: FastAPI service (directory: `main/`)
  - Entry point: `main/main.py`
  - Routers: `main/endpoints/*.py`
  - Schemas/ORM models: `main/schema/*.py` (SQLModel -> SQLAlchemy)
  - Database: PostgreSQL (sync + async engines)
  - Migrations: Alembic (`alembic.ini`, `migrations/` with `env.py` dynamic imports)
  - Caching & rate limiting: Redis via `fastapi-cache2` and `fastapi-limiter`
  - Config: environment variables, CORS settings in app
  - Logging: `main/logging_config.json`
  - File storage: user-uploaded images to `fotos/` via `main/core/foto_functions.py`
- Auth service: Django project under `django_auth/`
  - Django project: `django_auth/auth_project/` (settings, urls, wsgi/asgi)
  - Django app: `django_auth/authentication/`
    - Endpoints: `/auth/login`, `/auth/verify`, `/auth/refresh`, API key CRUD & verify
    - Auth: DRF + SimpleJWT + custom API key models & permissions; Redis cache configured
  - Served via gunicorn (see `django_auth/run.sh`)
- Cross-integration: FastAPI calls Django auth logic via in-process imports
  - `main/core/auth.py` sets up Django and imports `authentication.services` to verify
  - Supports both Bearer JWT and ApiKey header (with endpoint allowlist enforcement)

### 1.2 API Endpoints and Routes (FastAPI)
From `main/main.py` included routers + prefixes:
- `/absen-pengajian` (router: `endpoints/absen_pengajian.py`) [confirm source present]
- `/absen-asramaan` (router: `endpoints/absen_asramaan.py`) [confirm source present]
- `/biodata/generus` (router: `biodata_generus.py`)
- `/data/daerah` (router: `data_daerah.py`)
- `/data/sesi` (router: `sesi.py`) [confirm source present]
- `/url` (router: `url.py`)
- `/data/materi` (router: `data_materi.py`)
- `/data/hobi` (router: `data_hobi.py`)
- `/data/kelas-sekolah` (router: `data_kelas_sekolah.py`)
- `/wilayah` (router: `wilayah.py`, proxies to `https://emsifa.github.io/api-wilayah-indonesia/api/`)
- Root: `GET /` → `{ "api": "online" }`

Notes:
- The repo contains compiled caches for `absen_*` and `sesi`, but sources aren’t visible in `main/endpoints` listing; verify presence in version control and enumerate endpoints exactly.
- Example details identified:
  - `url.py`: `GET /url/{code}` (404 on not found), `POST /url/` (Form data, rate limited 10/min) with auth via `verify_token`
  - `wilayah.py`: multiple `GET` routes proxying upstream; returns JSONResponse and adds CORS header
  - `data_hobi.py`: async DB reads using `AsyncSession`, returns list of category/hobby
  - `biodata_generus.py`: CRUD-like routes with read/write permission checks, image upload/save

### 1.3 Database Models and Relationships (SQLModel)
Identified models (tables retain legacy names):
- `url_schema.URL` → table `rec_shorten_urls` with `id`, `url`, `url_code (unique)`
- `biodata_generus_schema.BiodataGenerusModel` → table `data_biodata_generus` with many columns and custom `biodata_id` (unique), image fields, created_at
- `data_hobi_schema.DataHobi` → table `data_hobi` with `kategori`, `hobi`
- Additional schemas exist: `data_daerah_schema.py`, `data_kelas_sekolah_schema.py`, `data_materi_schema.py` (inspect to fully map columns)
- Alembic is configured to import all modules under `main/` recursively to collect metadata for migrations.

### 1.4 Middleware and Authentication
- CORS middleware: restricted to specific origins
- Rate limiting: `fastapi-limiter` backed by Redis, per-endpoint dependencies
- Caching: `fastapi-cache2` with `RedisBackend`
- Auth dependency: `verify_token` in `main/core/auth.py` supports:
  - `Authorization: Bearer <jwt>` → verified via Django `services.verify_token_logic`, grants `read_write`
  - `Authorization: ApiKey <key>` → verified via Django `services.verify_api_key_logic`, optional allowed endpoint prefix list filter (supports both with and without `/api` prefix)
- Permission wrappers: `verify_read_permission`, `verify_write_permission`

### 1.5 Configuration Management
- Env-driven: `POSTGRES_*`, `REDIS_CONTAINER_NAME`, Django `SECRET_KEY` etc.
- `main/core/db.py` builds DSNs with fallbacks; sync/async engines created
- Alembic uses env or `alembic.ini` (has a static DSN but runtime `env.py` overrides from env)
- `support/` contains deployment scripts (`uvicorn.sh`, `nginx.conf`, `secrets.env`, `env.conf`)

### 1.6 Dependencies and Integrations
- Python libs: FastAPI, Uvicorn, SQLModel/SQLAlchemy, Alembic, DRF, SimpleJWT, Redis, fastapi-cache2, fastapi-limiter, Pillow, Prometheus client, structlog, requests/httpx
- External APIs: Wilayah proxy to `emsifa.github.io`
- Storage: local filesystem `fotos/` for images


## 2) Target Go Architecture Design

### 2.1 Framework and Libraries
- Web framework: Gin (or Echo/Fiber). Recommendation: Gin for ecosystem maturity & middleware.
- Database:
  - ORM/Query: GORM (most popular, pragmatic) or Ent (schema-first). Recommendation: GORM for faster parity with SQLModel, plus sqlc for critical complex queries if needed.
  - Migrations: golang-migrate with SQL migration files to match current schema. Where helpful, use `atlas` or `goose`, but `migrate` is standard.
- Auth & Security:
  - JWT: `github.com/golang-jwt/jwt/v5` (replicate SimpleJWT claims/validation)
  - API keys: custom table and middleware; enforce allowed endpoint prefixes
  - CORS: `github.com/gin-contrib/cors`
  - Rate limiting: Redis-backed limiter (e.g., `github.com/ulule/limiter/v3` with `redis` store or `go-redis/redis_rate`)
  - Caching: Redis via `github.com/redis/go-redis/v9` + app-layer cache. For in-memory cache, `github.com/dgraph-io/ristretto` if needed.
- HTTP client: `net/http` with timeouts or `github.com/go-resty/resty/v2` for convenience (for Wilayah proxy)
- File uploads: Gin’s multipart handling; save under `fotos/` with equivalent naming rules
- Config: `spf13/viper` with env overrides; `.env` optional
- Logging: `uber-go/zap` or `sirupsen/logrus`; request logging middleware; structured JSON
- Metrics: `prometheus/client_golang`

### 2.2 Project Structure
```
/gnrs-go
  /cmd/api (main entry)
  /internal
    /http
      /middleware (auth, cors, rate-limit, logging, recovery)
      /routes (router registration)
      /handlers (per-domain handlers)
      /responses (error helpers)
    /auth (jwt manager, api-key verifier, permission helpers)
    /config (viper setup)
    /db (gorm init, migrations)
    /models (gorm models mirroring SQLModel tables)
    /repos (data access; optional if not using GORM directly in handlers)
    /services (business logic)
    /cache (redis clients, caching helpers)
    /files (foto storage utils)
    /clients (wilayah proxy client)
  /migrations (SQL files)
  /scripts (dev, run, build)
```

### 2.3 Routing and Handlers
- Preserve exact route structure/prefixes, e.g.:
  - `/` health route
  - `/url` (GET `/{code}`, POST `/` with form data)
  - `/biodata/generus`, `/data/daerah`, `/data/sesi`, `/data/hobi`, `/data/materi`, `/data/kelas-sekolah`, `/wilayah/*`
  - Auth microservice endpoints replicated: `/auth/login`, `/auth/verify`, `/auth/refresh`, `/auth/apikeys/*`
- Group routes by domain; attach middlewares per group where needed (e.g., rate limit, auth)

### 2.4 Models and DB Mapping (PostgreSQL)
- Define GORM models with:
  - Same table names via `TableName()` or struct tags (e.g., `rec_shorten_urls`, `data_biodata_generus`, `data_hobi`)
  - Same columns (types, constraints, indexes). For JSON fields, use `jsonb` and `type:jsonb` tags
- Ensure migrations match existing Alembic-managed schema. Avoid destructive changes. If differences exist, author companion migrations.

### 2.5 Auth and Permissions
- JWT flows:
  - Implement `/auth/login` to issue JWT using configured secret, lifetimes identical to SimpleJWT (Access 15m, Refresh 75m) and `Bearer` handling
  - `/auth/refresh` with refresh token validation
  - `/auth/verify` to validate token
- API keys:
  - Recreate Django’s `authentication.apikey_models` in Go; model: key, status (active/revoked), optional `allowed_endpoints` list
  - Middleware to enforce `Authorization: ApiKey ...`, validate format and allowed endpoint prefixes (with and without `/api`), exact or subtree match
- Permission helpers equivalent to `verify_read_permission` and `verify_write_permission`

### 2.6 Middleware & Cross-cutting
- CORS: mirror `allow_origins`, methods, headers, credentials
- Rate limiting: configure Redis-backed limiter and per-handler policies (e.g., `POST /url` 10/min)
- Caching: optional response caching as needed; for parity with fastapi-cache2, implement explicit cached lookups where applicable
- Logging: structured logs with request IDs, latencies; align levels

### 2.7 Configuration
- Viper-backed configuration loading environment variables used today: `POSTGRES_*`, `REDIS_CONTAINER_NAME`, JWT secrets, CORS origins, etc.
- Provide defaults matching current fallbacks in `main/core/db.py` where reasonable


## 3) API Compatibility Requirements
- Methods, paths, and parameters: identical to current FastAPI/Django behavior
  - Keep form vs JSON payload handling (e.g., `POST /url` expects form `url`)
  - Keep path params and catch-all routes (e.g., `/wilayah/{full_code:path}` → wildcard in Gin)
- Responses: same JSON shapes and field names; maintain 404/401/403/422/500/502 status semantics
- Authentication:
  - Accept both `Authorization: Bearer <jwt>` and `Authorization: ApiKey <key>`
  - Endpoint allowlist behavior for API keys, including `/api/`-prefixed comparisons
- CORS headers and behavior: same origins and headers to avoid client breakage
- File upload rules: PNG only; file size validation; filename format unchanged; serve endpoints identical


## 4) Migration Strategy

### 4.1 Database Migration Strategy
- Target: zero or minimal schema changes. Reuse existing PostgreSQL database.
- Inventory and map every table/column/index from SQLModel + Django API key models. Create equivalent GORM models.
- Use `golang-migrate` to:
  - Create no-op baseline migration matching current schema (or generate SQL from current DB state)
  - Add targeted migrations only if gaps exist
- Validate with a read-only staging DB clone before production.

### 4.2 Authentication Service Migration
- Phase 1 (Bridge): Keep Django auth endpoints running; Go service verifies tokens/API keys by calling Django HTTP endpoints (or shared DB tables) to avoid blocking. This preserves behavior while Go is built.
- Phase 2 (Replace): Implement Go-native auth endpoints (`/auth/*`) and middleware; migrate API key data into Go-managed tables (either reusing same tables with identical schema or perform an in-place migration). Update FastAPI-ported handlers to call Go auth locally.
- Phase 3 (Decommission): Remove Django once parity is verified and traffic is cut over.

### 4.3 Service Cutover Strategy
- Deploy Go service alongside Python services behind Nginx/ingress.
- Blue–green or canary:
  - Blue–green: switch traffic by hostname/route once parity tests pass
  - Canary: weighted routing for specific endpoints or small percentage of traffic; gradually increase
- Route-by-endpoint rollout: move low-risk endpoints first (e.g., `/url`, `/wilayah`), then data endpoints, finally `biodata/generus` and any stateful writes

### 4.4 Data Migration Procedures
- If reusing the same DB schema: no data migration needed beyond ensuring compatible ORM mappings
- For API keys and users:
  - Export/import keys if schema changes occur; otherwise reuse same tables from Django
  - For JWT, preserve secret and claim format to validate existing tokens during rollout

### 4.5 Testing Strategy (Functional Equivalence)
- Contract tests (golden tests):
  - Generate request/response fixtures for each endpoint from the current Python services
  - Run the same tests against the Go service; diff JSON bodies, headers, status codes
- Schema tests:
  - Validate GORM models against live DB (column names, types, constraints)
- Auth tests:
  - JWT: login/verify/refresh flows with expiry windows
  - API key: allowed endpoint list, revoked keys, incorrect format
- Performance and rate limit tests:
  - Ensure Redis-based throttles match semantics; attempt to burst and validate
- Integration tests:
  - Upstream proxy (Wilayah) timeouts, error mapping to 502; network failures
- File handling tests:
  - PNG validation, filename conventions, size limits, 404 on missing files

### 4.6 Rollback Procedures
- Maintain ability to revert Nginx routing to the Python services instantly
- Keep DB migrations backward-compatible or reversible; avoid destructive changes during rollout
- Maintain both auth systems during canary window; flipping env flags switches verification provider


## 5) Implementation Timeline (Phased)

Phase 0 – Discovery & Planning (1–2 weeks)
- Complete endpoint inventory (including `absen_*`, `sesi`) and data model catalog
- Confirm DB schema from staging/production
- Decide final Go stack choices and skeleton repo setup

Phase 1 – Foundations (1–2 weeks)
- Bootstrap Go project (Gin, config, logging, DB, Redis, migrations)
- Implement health, CORS, common middleware, error handling
- Implement Redis clients, rate limiter, and base auth middleware interfaces

Phase 2 – Read-only Endpoints (2–3 weeks)
- Implement `/wilayah/*` proxy; `/url/{code}` GET
- Implement `/data/*` read endpoints (daerah/hobi/kelas-sekolah/materi/sesi)
- Contract tests to ensure parity

Phase 3 – Write endpoints and Files (2–4 weeks)
- Implement `/url` POST (form data + rate limit + auth)
- Implement `/biodata/generus` (create/read/detail; photo upload/save/serve) with same validations
- Contract, integration, and file-handling tests

Phase 4 – Auth Migration (2–4 weeks)
- Phase 1 bridge: integrate with Django auth HTTP endpoints or reuse tables
- Phase 2 replace: implement `/auth/*` in Go (JWT + API key admin and verify)
- Data alignment/migration if needed; cut over and monitor

Phase 5 – Cutover & Decommission (1–2 weeks)
- Canary release → increase traffic
- Observability, metrics, error budgets; rollback ready
- Decommission Python services after stability window

Total estimated engineering effort: ~8–14 weeks (team of 2–3 engineers), depending on endpoint complexity not fully visible yet.


## 6) Risk Assessment and Mitigations
1) Hidden endpoint/source gaps (e.g., `absen_*`, `sesi` not visible)
- Mitigation: Complete inventory; fail build if any router lacks a Go mapping
2) Auth parity (SimpleJWT nuances, API key endpoint allowlist semantics)
- Mitigation: Reuse secrets/claims; encode identical permission model; extensive contract tests
3) ORM differences (SQLModel → GORM/Ent)
- Mitigation: Start with DB-first approach; mirror table/column names exactly; approve mappings with DB introspection tests
4) Rate limiting and caching behavior mismatch
- Mitigation: Use Redis-backed limiter; write behavior tests; compare counters under load
5) File handling differences
- Mitigation: Keep identical PNG-only rules and filename format; preserve directory layout
6) Proxy and networking behavior
- Mitigation: Configure HTTP client timeouts and error mapping to 502; retries where appropriate (idempotent reads)
7) Rollback complexity
- Mitigation: Blue–green with instant route flip; reversible DB migrations; dual auth window
8) Operational config drift
- Mitigation: Centralize config with Viper; provide sample `.env`; document required vars


## 7) Detailed Compatibility Checklist (Go Implementation)
- [ ] Routes: All prefixes and paths registered identically; wildcard paths honored
- [ ] Methods: Identical HTTP verbs
- [ ] Params: Path, query, and form parsing preserved
- [ ] Bodies: JSON shapes and camel/snake case maintained
- [ ] Status codes: 200/201/401/403/404/422/500/502 matched
- [ ] Headers: CORS and `Access-Control-Allow-Origin` where used (e.g., Wilayah)
- [ ] Auth: `Bearer` and `ApiKey` handling; permission checks read/write
- [ ] Rate limits: Values and buckets match (e.g., 10/min on POST /url)
- [ ] Caching: Where previously cached, provide same freshness semantics (if externally observable)
- [ ] Files: PNG-only, size limit, filename pattern, serve path


## 8) Deliverables
- New Go repository with full implementation and CI
- Migration SQL files and tooling scripts
- Contract and integration test suites with golden fixtures
- Ops docs: configuration matrix, runbooks, rollback steps
- Cutover plan and validation report


## 9) Next Actions
- Confirm complete endpoint list and schemas (including any missing `endpoints/*.py`)
- Approve Go stack selection (Gin + GORM + golang-migrate + go-redis + zap + viper)
- Set up staging infra (Postgres, Redis, Nginx/ingress) to begin parity work

