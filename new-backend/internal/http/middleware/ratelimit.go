package middleware

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"gnrs-go/internal/cache"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
)

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	Requests int           // Number of requests allowed
	Window   time.Duration // Time window for the requests
	KeyFunc  func(*gin.Context) string // Function to generate rate limit key
}

// RateLimitMiddleware creates a Redis-backed rate limiting middleware
func RateLimitMiddleware(redisClient *cache.Client, config RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := config.KeyFunc(c)
		if key == "" {
			c.Next()
			return
		}

		// Create rate limit key
		rateLimitKey := fmt.Sprintf("rate_limit:%s", key)

		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		// Increment counter with expiry
		count, err := redisClient.IncrementWithExpiry(ctx, rateLimitKey, config.Window)
		if err != nil {
			// If Redis is down, allow the request but log the error
			c.Next()
			return
		}

		// Check if rate limit exceeded
		if count > int64(config.Requests) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"retry_after": int(config.Window.Seconds()),
			})
			c.Abort()
			return
		}

		// Add rate limit headers
		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", config.Requests))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", max(0, int64(config.Requests)-count)))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", time.Now().Add(config.Window).Unix()))

		c.Next()
	}
}

// IPBasedRateLimit creates a rate limiter based on client IP
func IPBasedRateLimit(redisClient *cache.Client, requests int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Requests: requests,
		Window:   window,
		KeyFunc: func(c *gin.Context) string {
			return c.ClientIP()
		},
	}
	return RateLimitMiddleware(redisClient, config)
}

// EndpointBasedRateLimit creates a rate limiter based on endpoint and IP
func EndpointBasedRateLimit(redisClient *cache.Client, requests int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Requests: requests,
		Window:   window,
		KeyFunc: func(c *gin.Context) string {
			return fmt.Sprintf("%s:%s", c.Request.URL.Path, c.ClientIP())
		},
	}
	return RateLimitMiddleware(redisClient, config)
}

// UserBasedRateLimit creates a rate limiter based on authenticated user
func UserBasedRateLimit(redisClient *cache.Client, requests int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Requests: requests,
		Window:   window,
		KeyFunc: func(c *gin.Context) string {
			// Try to get user from auth context
			if authResult, exists := c.Get("auth_result"); exists {
				if result, ok := authResult.(*models.AuthResult); ok && result.UserID != nil {
					return fmt.Sprintf("user:%d", *result.UserID)
				}
			}
			// Fallback to IP if no user context
			return c.ClientIP()
		},
	}
	return RateLimitMiddleware(redisClient, config)
}

func max(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}
