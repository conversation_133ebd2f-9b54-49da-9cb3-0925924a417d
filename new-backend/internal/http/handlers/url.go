package handlers

import (
	"gnrs-go/internal/cache"
	"gnrs-go/internal/db"
	"gnrs-go/internal/http/responses"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// URLHandler handles URL shortening endpoints
type URLHandler struct {
	db    *db.DB
	cache *cache.Client
}

// NewURLHandler creates a new URL handler
func NewURLHandler(database *db.DB, cacheClient *cache.Client) *URLHandler {
	return &URLHandler{
		db:    database,
		cache: cacheClient,
	}
}

// GetURL handles GET /url/{code}
func (h *URLHandler) GetURL(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		responses.BadRequest(c, "URL code is required")
		return
	}

	var url models.URL
	if err := h.db.Where("url_code = ?", code).First(&url).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			responses.NotFound(c, "URL not found")
			return
		}
		responses.InternalServerError(c, "Database error")
		return
	}

	response := models.URLResponse{
		URL:     url.URL,
		URLCode: url.URLCode,
	}

	responses.Success(c, response)
}

// CreateURL handles POST /url
func (h *URLHandler) CreateURL(c *gin.Context) {
	var req models.URLCreate
	if err := c.ShouldBind(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	if req.URL == "" {
		responses.BadRequest(c, "URL is required")
		return
	}

	// Create URL record
	url := models.URL{
		URL: req.URL,
	}

	if err := h.db.Create(&url).Error; err != nil {
		responses.InternalServerError(c, "Failed to create URL")
		return
	}

	response := models.URLResponse{
		URL:     url.URL,
		URLCode: url.URLCode,
	}

	responses.Created(c, response)
}
