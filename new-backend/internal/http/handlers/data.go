package handlers

import (
	"gnrs-go/internal/cache"
	"gnrs-go/internal/db"
	"gnrs-go/internal/http/responses"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
)

// DataHandler handles data endpoints
type DataHandler struct {
	db    *db.DB
	cache *cache.Client
}

// NewDataHandler creates a new data handler
func NewDataHandler(database *db.DB, cacheClient *cache.Client) *DataHandler {
	return &DataHandler{
		db:    database,
		cache: cacheClient,
	}
}

// GetHobiData handles GET /data/hobi
func (h *DataHandler) GetHobiData(c *gin.Context) {
	var hobbies []models.DataHobi
	if err := h.db.Find(&hobbies).Error; err != nil {
		responses.InternalServerError(c, "Database error")
		return
	}

	// Convert to response format
	var result []models.HobiResponse
	for _, hobby := range hobbies {
		result = append(result, models.HobiResponse{
			Kategori: hobby.Kategori,
			Hobi:     hobby.Hobi,
		})
	}

	responses.Success(c, result)
}

// GetDaerahData handles GET /data/daerah/{daerah}
func (h *DataHandler) GetDaerahData(c *gin.Context) {
	daerah := c.Param("daerah")
	if daerah == "" {
		responses.BadRequest(c, "Daerah parameter is required")
		return
	}

	var daerahData []models.DataDaerah
	if err := h.db.Where("daerah = ?", daerah).Find(&daerahData).Error; err != nil {
		responses.InternalServerError(c, "Database error")
		return
	}

	if len(daerahData) == 0 {
		responses.NotFound(c, "No data found for daerah: "+daerah)
		return
	}

	// Convert to response format
	var result []models.DaerahResponse
	for _, data := range daerahData {
		result = append(result, models.DaerahResponse{
			Ranah:       data.Ranah,
			DetailRanah: data.DetailRanah,
		})
	}

	responses.Success(c, result)
}

// GetKelasSekolahData handles GET /data/kelas-sekolah
func (h *DataHandler) GetKelasSekolahData(c *gin.Context) {
	var kelasSekolah []models.DataKelasSekolah
	if err := h.db.Find(&kelasSekolah).Error; err != nil {
		responses.InternalServerError(c, "Database error")
		return
	}

	// Convert to response format
	var result []models.KelasSekolahResponse
	for _, kelas := range kelasSekolah {
		result = append(result, models.KelasSekolahResponse{
			Jenjang: kelas.Jenjang,
			Kelas:   kelas.Kelas,
		})
	}

	responses.Success(c, result)
}

// GetMateriData handles GET /data/materi/{kategori} and GET /data/materi/{kategori}/{detail_kategori}
func (h *DataHandler) GetMateriData(c *gin.Context) {
	kategori := c.Param("kategori")
	if kategori == "" {
		responses.BadRequest(c, "Kategori parameter is required")
		return
	}

	detailKategori := c.Param("detail_kategori")

	// Build query
	query := h.db.Where("kategori = ?", kategori)
	if detailKategori != "" {
		query = query.Where("detail_kategori = ?", detailKategori)
	}

	var materiData []models.DataMateri
	if err := query.Find(&materiData).Error; err != nil {
		responses.InternalServerError(c, "Database error")
		return
	}

	if len(materiData) == 0 {
		errorMsg := "No data found for kategori: " + kategori
		if detailKategori != "" {
			errorMsg += " and detail_kategori: " + detailKategori
		}
		responses.NotFound(c, errorMsg)
		return
	}

	// Convert to response format
	var result []models.MateriResponse
	for _, materi := range materiData {
		result = append(result, models.MateriResponse{
			Kategori:         materi.Kategori,
			DetailKategori:   materi.DetailKategori,
			Materi:           materi.Materi,
			DetailMateri:     materi.DetailMateri,
			Indikator:        materi.Indikator,
			IndikatorMulai:   materi.IndikatorMulai,
			IndikatorAkhir:   materi.IndikatorAkhir,
		})
	}

	responses.Success(c, result)
}
