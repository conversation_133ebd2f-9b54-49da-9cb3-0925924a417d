package handlers

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gnrs-go/internal/cache"
	"gnrs-go/internal/db"
	"gnrs-go/internal/files"
	"gnrs-go/internal/http/responses"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BiodataHandler handles biodata generus endpoints
type BiodataHandler struct {
	db          *db.DB
	cache       *cache.Client
	fotoService *files.FotoService
}

// NewBiodataHandler creates a new biodata handler
func NewBiodataHandler(database *db.DB, cacheClient *cache.Client, fotoService *files.FotoService) *BiodataHandler {
	return &BiodataHandler{
		db:          database,
		cache:       cacheClient,
		fotoService: fotoService,
	}
}

// CreateBiodata handles POST /biodata/generus
func (h *BiodataHandler) CreateBiodata(c *gin.Context) {
	// Parse form data
	var biodata models.BiodataGenerus
	
	// Handle both form data and JSON input
	contentType := c.GetHeader("Content-Type")
	
	if strings.Contains(contentType, "application/json") {
		// Handle JSON input
		if err := c.ShouldBindJSON(&biodata); err != nil {
			responses.BadRequest(c, "Invalid JSON format")
			return
		}
	} else {
		// Handle form data
		if err := h.parseFormData(c, &biodata); err != nil {
			responses.BadRequest(c, err.Error())
			return
		}
	}

	// Validate required fields
	if err := h.validateBiodata(&biodata); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	// Generate unique biodata ID
	for attempts := 0; attempts < 10; attempts++ {
		candidateID := generateBiodataID(6)
		var existing models.BiodataGenerus
		if err := h.db.Where("biodata_id = ?", candidateID).First(&existing).Error; err == gorm.ErrRecordNotFound {
			biodata.BiodataID = candidateID
			break
		}
	}

	if biodata.BiodataID == "" {
		responses.InternalServerError(c, "Failed to generate unique biodata ID")
		return
	}

	// Handle photo upload if provided
	if file, err := c.FormFile("foto"); err == nil && file != nil {
		// Validate photo size
		if err := h.fotoService.ValidateFotoSize(file.Size); err != nil {
			responses.UnprocessableEntity(c, err.Error())
			return
		}

		// Save photo
		fotoID, filename, err := h.fotoService.SaveFoto(file, biodata.BiodataID, biodata.NamaLengkap, time.Now())
		if err != nil {
			responses.BadRequest(c, "Photo upload failed: "+err.Error())
			return
		}

		biodata.FotoID = &fotoID
		biodata.FotoFilename = &filename
	}

	// Set created_at
	biodata.CreatedAt = time.Now().Format("2006-01-02")

	// Save to database
	if err := h.db.Create(&biodata).Error; err != nil {
		responses.InternalServerError(c, "Failed to create biodata")
		return
	}

	// Convert to response format
	response := models.BiodataGenerusResponse{
		ID:              biodata.ID,
		BiodataID:       biodata.BiodataID,
		NamaLengkap:     biodata.NamaLengkap,
		NamaPanggilan:   biodata.NamaPanggilan,
		SambungDesa:     biodata.SambungDesa,
		SambungKelompok: biodata.SambungKelompok,
		JenisKelamin:    biodata.JenisKelamin,
		FotoFilename:    biodata.FotoFilename,
		FotoID:          biodata.FotoID,
		CreatedAt:       biodata.CreatedAt,
	}

	responses.Created(c, response)
}

// GetBiodata handles GET /biodata/generus?daerah=...
func (h *BiodataHandler) GetBiodata(c *gin.Context) {
	daerah := c.Query("daerah")
	if daerah == "" {
		responses.BadRequest(c, "daerah parameter is required")
		return
	}

	var biodataList []models.BiodataGenerus
	if err := h.db.Where("daerah = ?", daerah).Find(&biodataList).Error; err != nil {
		responses.InternalServerError(c, "Database error")
		return
	}

	// Convert to response format
	var result []models.BiodataGenerusGetResponse
	for _, biodata := range biodataList {
		result = append(result, models.BiodataGenerusGetResponse{
			BiodataID:       biodata.BiodataID,
			NamaLengkap:     biodata.NamaLengkap,
			NamaPanggilan:   biodata.NamaPanggilan,
			SambungDesa:     biodata.SambungDesa,
			SambungKelompok: biodata.SambungKelompok,
			JenisKelamin:    biodata.JenisKelamin,
		})
	}

	responses.Success(c, result)
}

// GetBiodataByID handles GET /biodata/generus/{biodata_id}
func (h *BiodataHandler) GetBiodataByID(c *gin.Context) {
	biodataID := c.Param("biodata_id")
	
	// Validate biodata_id format (6 characters, alphanumeric)
	if len(biodataID) != 6 || !isAlphanumeric(biodataID) {
		responses.BadRequest(c, "Invalid biodata ID format. Must be 6 alphanumeric characters.")
		return
	}

	var biodata models.BiodataGenerus
	if err := h.db.Where("biodata_id = ?", biodataID).First(&biodata).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			responses.NotFound(c, fmt.Sprintf("Biodata with ID %s not found", biodataID))
			return
		}
		responses.InternalServerError(c, "Database error")
		return
	}

	// Convert to detailed response format
	response := models.BiodataGenerusDetailResponse{
		BiodataID:             biodata.BiodataID,
		NamaLengkap:           biodata.NamaLengkap,
		NamaPanggilan:         biodata.NamaPanggilan,
		KelahiranTempat:       biodata.KelahiranTempat,
		KelahiranTanggal:      biodata.KelahiranTanggal,
		AlamatJalan:           biodata.AlamatJalan,
		AlamatNomor:           biodata.AlamatNomor,
		AlamatProvinsi:        biodata.AlamatProvinsi,
		AlamatKabupatenKota:   biodata.AlamatKabupatenKota,
		AlamatKecamatan:       biodata.AlamatKecamatan,
		AlamatKelurahanDesa:   biodata.AlamatKelurahanDesa,
		PendataanTanggal:      biodata.PendataanTanggal,
		SambungDesa:           biodata.SambungDesa,
		SambungKelompok:       biodata.SambungKelompok,
		Hobi:                  biodata.Hobi,
		SekolahKelas:          biodata.SekolahKelas,
		NomorHape:             biodata.NomorHape,
		NamaAyah:              biodata.NamaAyah,
		NamaIbu:               biodata.NamaIbu,
		StatusAyah:            biodata.StatusAyah,
		StatusIbu:             biodata.StatusIbu,
		NomorHapeAyah:         biodata.NomorHapeAyah,
		NomorHapeIbu:          biodata.NomorHapeIbu,
		JenisKelamin:          biodata.JenisKelamin,
		Daerah:                biodata.Daerah,
		FotoFilename:          biodata.FotoFilename,
		FotoID:                biodata.FotoID,
		CreatedAt:             biodata.CreatedAt,
	}

	responses.Success(c, response)
}

// ServeFoto handles GET /biodata/generus/foto/{filename}
func (h *BiodataHandler) ServeFoto(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		responses.BadRequest(c, "Filename is required")
		return
	}

	filePath, err := h.fotoService.ServeFoto(filename)
	if err != nil {
		responses.NotFound(c, "Foto not found")
		return
	}

	// Determine media type
	mediaType := files.GetMediaType(filename)
	
	c.Header("Content-Type", mediaType)
	c.File(filePath)
}

// Helper functions

func (h *BiodataHandler) parseFormData(c *gin.Context, biodata *models.BiodataGenerus) error {
	// Parse basic string fields
	biodata.NamaLengkap = c.PostForm("nama_lengkap")
	biodata.NamaPanggilan = c.PostForm("nama_panggilan")
	biodata.KelahiranTempat = c.PostForm("kelahiran_tempat")
	biodata.AlamatJalan = c.PostForm("alamat_jalan")
	biodata.AlamatProvinsi = c.PostForm("alamat_provinsi")
	biodata.AlamatKabupatenKota = c.PostForm("alamat_kabupatenkota")
	biodata.AlamatKecamatan = c.PostForm("alamat_kecamatan")
	biodata.AlamatKelurahanDesa = c.PostForm("alamat_kelurahandesa")
	biodata.SambungDesa = c.PostForm("sambung_desa")
	biodata.SambungKelompok = c.PostForm("sambung_kelompok")
	biodata.SekolahKelas = c.PostForm("sekolah_kelas")
	biodata.NamaAyah = c.PostForm("nama_ayah")
	biodata.NamaIbu = c.PostForm("nama_ibu")
	biodata.StatusAyah = c.PostForm("status_ayah")
	biodata.StatusIbu = c.PostForm("status_ibu")
	biodata.JenisKelamin = c.PostForm("jenis_kelamin")
	biodata.Daerah = c.PostForm("daerah")

	// Parse optional string fields
	if alamatNomor := c.PostForm("alamat_nomor"); alamatNomor != "" {
		biodata.AlamatNomor = &alamatNomor
	}
	if nomorHape := c.PostForm("nomor_hape"); nomorHape != "" {
		biodata.NomorHape = &nomorHape
	}
	if nomorHapeAyah := c.PostForm("nomor_hape_ayah"); nomorHapeAyah != "" {
		biodata.NomorHapeAyah = &nomorHapeAyah
	}
	if nomorHapeIbu := c.PostForm("nomor_hape_ibu"); nomorHapeIbu != "" {
		biodata.NomorHapeIbu = &nomorHapeIbu
	}

	// Parse date fields
	if kelahiranTanggal := c.PostForm("kelahiran_tanggal"); kelahiranTanggal != "" {
		if date, err := time.Parse("2006-01-02", kelahiranTanggal); err == nil {
			biodata.KelahiranTanggal = date
		} else {
			return fmt.Errorf("invalid kelahiran_tanggal format: %s", err.Error())
		}
	}

	if pendataanTanggal := c.PostForm("pendataan_tanggal"); pendataanTanggal != "" {
		if date, err := time.Parse("2006-01-02", pendataanTanggal); err == nil {
			biodata.PendataanTanggal = date
		} else {
			return fmt.Errorf("invalid pendataan_tanggal format: %s", err.Error())
		}
	}

	// Parse hobi JSON
	if hobiStr := c.PostForm("hobi"); hobiStr != "" {
		// Validate hobi doesn't contain square brackets
		if strings.Contains(hobiStr, "[") || strings.Contains(hobiStr, "]") {
			return fmt.Errorf("invalid hobi format: square brackets are not allowed")
		}

		var hobiMap models.HobiMap
		if err := json.Unmarshal([]byte(hobiStr), &hobiMap); err != nil {
			return fmt.Errorf("invalid hobi JSON format: %s", err.Error())
		}
		biodata.Hobi = hobiMap
	}

	return nil
}

func (h *BiodataHandler) validateBiodata(biodata *models.BiodataGenerus) error {
	requiredFields := map[string]string{
		"nama_lengkap":           biodata.NamaLengkap,
		"nama_panggilan":         biodata.NamaPanggilan,
		"kelahiran_tempat":       biodata.KelahiranTempat,
		"alamat_jalan":           biodata.AlamatJalan,
		"alamat_provinsi":        biodata.AlamatProvinsi,
		"alamat_kabupatenkota":   biodata.AlamatKabupatenKota,
		"alamat_kecamatan":       biodata.AlamatKecamatan,
		"alamat_kelurahandesa":   biodata.AlamatKelurahanDesa,
		"sambung_desa":           biodata.SambungDesa,
		"sambung_kelompok":       biodata.SambungKelompok,
		"sekolah_kelas":          biodata.SekolahKelas,
		"nama_ayah":              biodata.NamaAyah,
		"nama_ibu":               biodata.NamaIbu,
		"status_ayah":            biodata.StatusAyah,
		"status_ibu":             biodata.StatusIbu,
		"jenis_kelamin":          biodata.JenisKelamin,
		"daerah":                 biodata.Daerah,
	}

	var missingFields []string
	for field, value := range requiredFields {
		if value == "" {
			missingFields = append(missingFields, field)
		}
	}

	// Check date fields
	if biodata.KelahiranTanggal.IsZero() {
		missingFields = append(missingFields, "kelahiran_tanggal")
	}
	if biodata.PendataanTanggal.IsZero() {
		missingFields = append(missingFields, "pendataan_tanggal")
	}

	if len(missingFields) > 0 {
		return fmt.Errorf("missing required fields: %s", strings.Join(missingFields, ", "))
	}

	return nil
}

func isAlphanumeric(s string) bool {
	matched, _ := regexp.MatchString("^[a-zA-Z0-9]+$", s)
	return matched
}

func generateBiodataID(length int) string {
	// This is a simplified version - in production you might want to use crypto/rand
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
