package handlers

import (
	"net/http"

	"gnrs-go/internal/auth"
	"gnrs-go/internal/http/responses"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	authService *auth.Service
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService *auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	tokens, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		responses.Unauthorized(c, err.Error())
		return
	}

	c.<PERSON>(http.StatusOK, tokens)
}

// Verify handles token verification
func (h *AuthHandler) Verify(c *gin.Context) {
	var req models.VerifyRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	result, err := h.authService.VerifyToken(c.Request.Context(), req.Token)
	if err != nil {
		responses.ServiceUnavailable(c, "Authentication service error")
		return
	}

	c.JSON(http.StatusOK, result)
}

// Refresh handles token refresh
func (h *AuthHandler) Refresh(c *gin.Context) {
	var req models.RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	tokens, err := h.authService.RefreshToken(c.Request.Context(), &req)
	if err != nil {
		responses.Unauthorized(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, tokens)
}

// CreateAPIKey handles API key creation
func (h *AuthHandler) CreateAPIKey(c *gin.Context) {
	// Get user from auth context
	authResult, exists := c.Get("auth_result")
	if !exists {
		responses.Unauthorized(c, "Authentication required")
		return
	}

	result := authResult.(*models.AuthResult)
	if result.UserID == nil {
		responses.Unauthorized(c, "Invalid authentication")
		return
	}

	var req models.APIKeyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	apiKey, err := h.authService.CreateAPIKey(c.Request.Context(), *result.UserID, &req)
	if err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	responses.Created(c, apiKey)
}
