package handlers

import (
	"net/http"

	"gnrs-go/internal/http/responses"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check endpoints
type HealthHandler struct{}

// NewHealthHandler creates a new health handler
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// Root handles the root endpoint
func (h *HealthHandler) Root(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"api": "online"})
}

// Health handles the health check endpoint
func (h *HealthHandler) Health(c *gin.Context) {
	responses.Success(c, gin.H{
		"status": "healthy",
		"timestamp": "2024-01-01T00:00:00Z", // You can use time.Now() here
	})
}
