package auth

import (
	"fmt"
	"time"

	"gnrs-go/internal/config"
	"gnrs-go/internal/models"

	"github.com/golang-jwt/jwt/v5"
)

// JWTManager handles JWT token operations
type JWTManager struct {
	secretKey           string
	accessTokenTTL      time.Duration
	refreshTokenTTL     time.Duration
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(cfg *config.Config) *JWTManager {
	return &JWTManager{
		secretKey:           cfg.JWTSecret,
		accessTokenTTL:      time.Duration(cfg.JWTAccessTokenTTL) * time.Second,
		refreshTokenTTL:     time.Duration(cfg.JWTRefreshTokenTTL) * time.Second,
	}
}

// Claims represents the JWT claims structure
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	TokenType string `json:"token_type"` // "access" or "refresh"
	jwt.RegisteredClaims
}

// GenerateTokens generates both access and refresh tokens for a user
func (j *J<PERSON>TManager) GenerateTokens(user *models.User) (*models.LoginResponse, error) {
	// Generate access token
	accessToken, err := j.generateToken(user, "access", j.accessTokenTTL)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Generate refresh token
	refreshToken, err := j.generateToken(user, "refresh", j.refreshTokenTTL)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &models.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int(j.accessTokenTTL.Seconds()),
	}, nil
}

// generateToken generates a JWT token with the specified type and TTL
func (j *JWTManager) generateToken(user *models.User, tokenType string, ttl time.Duration) (string, error) {
	now := time.Now()
	claims := &Claims{
		UserID:    user.ID,
		Username:  user.Username,
		TokenType: tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(ttl)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Subject:   fmt.Sprintf("%d", user.ID),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// VerifyToken verifies and parses a JWT token
func (j *JWTManager) VerifyToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.secretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}

// RefreshToken generates a new access token from a valid refresh token
func (j *JWTManager) RefreshToken(refreshTokenString string, user *models.User) (*models.LoginResponse, error) {
	claims, err := j.VerifyToken(refreshTokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	if claims.UserID != user.ID {
		return nil, fmt.Errorf("token user mismatch")
	}

	// Generate new access token
	accessToken, err := j.generateToken(user, "access", j.accessTokenTTL)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new access token: %w", err)
	}

	return &models.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshTokenString, // Keep the same refresh token
		TokenType:    "Bearer",
		ExpiresIn:    int(j.accessTokenTTL.Seconds()),
	}, nil
}

// ExtractUserID extracts the user ID from a token string
func (j *JWTManager) ExtractUserID(tokenString string) (uint, error) {
	claims, err := j.VerifyToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}
