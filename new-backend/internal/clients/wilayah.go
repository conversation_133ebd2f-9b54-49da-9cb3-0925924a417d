package clients

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// WilayahClient handles requests to the Wilayah API
type WilayahClient struct {
	client  *resty.Client
	baseURL string
}

// NewWilayahClient creates a new Wilayah API client
func NewWilayahClient() *WilayahClient {
	client := resty.New()
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(1 * time.Second)
	client.SetRetryMaxWaitTime(5 * time.Second)

	return &WilayahClient{
		client:  client,
		baseURL: "https://wilayah.id/api",
	}
}

// ProxyRequest makes a request to the Wilayah API and returns the response
func (w *WilayahClient) ProxyRequest(ctx context.Context, path string) (interface{}, error) {
	url := fmt.Sprintf("%s/%s.json", w.baseURL, path)

	resp, err := w.client.R().
		SetContext(ctx).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("failed to fetch data from upstream API: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("upstream API returned %d", resp.StatusCode())
	}

	// Parse JSON response
	var result interface{}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return result, nil
}

// GetProvinces fetches all provinces
func (w *WilayahClient) GetProvinces(ctx context.Context) (interface{}, error) {
	return w.ProxyRequest(ctx, "provinces")
}

// GetRegencies fetches regencies for a province
func (w *WilayahClient) GetRegencies(ctx context.Context, provinceCode string) (interface{}, error) {
	return w.ProxyRequest(ctx, fmt.Sprintf("regencies/%s", provinceCode))
}

// GetDistricts fetches districts for a regency
func (w *WilayahClient) GetDistricts(ctx context.Context, regencyCode string) (interface{}, error) {
	return w.ProxyRequest(ctx, fmt.Sprintf("districts/%s", regencyCode))
}

// GetVillages fetches villages for a district
func (w *WilayahClient) GetVillages(ctx context.Context, districtCode string) (interface{}, error) {
	return w.ProxyRequest(ctx, fmt.Sprintf("villages/%s", districtCode))
}
