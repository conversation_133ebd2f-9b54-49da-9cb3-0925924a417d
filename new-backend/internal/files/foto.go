package files

import (
	"crypto/rand"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"gnrs-go/internal/config"
)

// FotoService handles photo file operations
type FotoService struct {
	cfg *config.Config
}

// NewFotoService creates a new photo service
func NewFotoService(cfg *config.Config) *FotoService {
	return &FotoService{cfg: cfg}
}

// SaveFoto saves an uploaded photo and returns foto_id and filename
func (s *FotoService) SaveFoto(file *multipart.FileHeader, namaID, namaLengkap string, tanggal time.Time) (string, string, error) {
	// Validate file type (PNG only)
	if !strings.HasSuffix(strings.ToLower(file.Filename), ".png") {
		return "", "", fmt.Errorf("invalid file extension. Only .png files are allowed")
	}

	// Open the uploaded file
	src, err := file.Open()
	if err != nil {
		return "", "", fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	// Read first few bytes to verify PNG format
	header := make([]byte, 8)
	if _, err := src.Read(header); err != nil {
		return "", "", fmt.Errorf("failed to read file header: %w", err)
	}

	// PNG file signature: 89 50 4E 47 0D 0A 1A 0A
	pngSignature := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
	for i, b := range pngSignature {
		if header[i] != b {
			return "", "", fmt.Errorf("invalid file format. Only PNG files are allowed")
		}
	}

	// Reset file pointer
	src.Seek(0, 0)

	// Validate file size
	if file.Size > s.cfg.MaxFileSize {
		return "", "", fmt.Errorf("foto size must be ≤%d kB (2MB)", s.cfg.MaxFileSize/1024)
	}

	// Create fotos directory if it doesn't exist
	fotosDir := s.cfg.FotosDir
	if err := os.MkdirAll(fotosDir, 0755); err != nil {
		return "", "", fmt.Errorf("failed to create fotos directory: %w", err)
	}

	// Generate foto_id and create filename
	fotoID := generateFotoID(10)
	dateStr := tanggal.Format("20060102")
	sanitizedNama := sanitizeFilename(namaLengkap)

	// Format: {nama_id}_{sanitized_nama_lengkap}_{date_str}_{foto_id}.png
	filename := fmt.Sprintf("%s_%s_%s_%s.png", namaID, sanitizedNama, dateStr, fotoID)

	// Create destination file
	filePath := filepath.Join(fotosDir, filename)
	dst, err := os.Create(filePath)
	if err != nil {
		return "", "", fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	// Copy file content
	if _, err := io.Copy(dst, src); err != nil {
		return "", "", fmt.Errorf("failed to save foto: %w", err)
	}

	return fotoID, filename, nil
}

// ServeFoto serves a photo file by filename
func (s *FotoService) ServeFoto(filename string) (string, error) {
	filePath := filepath.Join(s.cfg.FotosDir, filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return "", fmt.Errorf("foto not found")
	}

	return filePath, nil
}

// ValidateFotoSize validates photo file size
func (s *FotoService) ValidateFotoSize(size int64) error {
	if size > s.cfg.MaxFileSize {
		return fmt.Errorf("foto size must be ≤%d kB (2MB)", s.cfg.MaxFileSize/1024)
	}
	return nil
}

// generateFotoID generates an alphanumeric string ID that doesn't start with 0
func generateFotoID(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"
	const firstCharset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"

	b := make([]byte, length)

	// Ensure first character is not 0 for better readability
	randomBytes := make([]byte, 1)
	rand.Read(randomBytes)
	b[0] = firstCharset[randomBytes[0]%byte(len(firstCharset))]

	// Generate remaining characters
	for i := 1; i < length; i++ {
		rand.Read(randomBytes)
		b[i] = charset[randomBytes[0]%byte(len(charset))]
	}

	return string(b)
}

// sanitizeFilename sanitizes text for use in filename
func sanitizeFilename(text string) string {
	// Remove special characters and spaces, keep only alphanumeric and underscores
	reg := regexp.MustCompile(`[^a-zA-Z0-9_]`)
	sanitized := reg.ReplaceAllString(strings.ReplaceAll(text, " ", "_"), "")

	// Convert to lowercase
	sanitized = strings.ToLower(sanitized)

	// Limit length to prevent overly long filenames
	if len(sanitized) > 50 {
		sanitized = sanitized[:50]
	}

	if sanitized == "" {
		sanitized = "unknown"
	}

	return sanitized
}

// GetMediaType returns the media type for a file extension
func GetMediaType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "image/jpeg"
	}
}
