package models

import (
	"crypto/rand"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// HobiMap represents the hobi field as a JSON map
type HobiMap map[string]string

// Value implements the driver.Valuer interface for database storage
func (h HobiMap) Value() (driver.Value, error) {
	if h == nil {
		return nil, nil
	}
	return json.Marshal(h)
}

// <PERSON>an implements the sql.Scanner interface for database retrieval
func (h *HobiMap) Scan(value interface{}) error {
	if value == nil {
		*h = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into HobiMap", value)
	}

	return json.Unmarshal(bytes, h)
}

// BiodataGenerus represents the biodata generus model
// Maps to table: data_biodata_generus
type BiodataGenerus struct {
	ID                    uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	BiodataID             string    `gorm:"unique;not null;size:6;index" json:"biodata_id"`
	NamaLengkap           string    `gorm:"not null" json:"nama_lengkap"`
	NamaPanggilan         string    `gorm:"not null" json:"nama_panggilan"`
	KelahiranTempat       string    `gorm:"not null" json:"kelahiran_tempat"`
	KelahiranTanggal      time.Time `gorm:"type:date;not null" json:"kelahiran_tanggal"`
	AlamatJalan           string    `gorm:"not null" json:"alamat_jalan"`
	AlamatNomor           *string   `json:"alamat_nomor"`
	AlamatProvinsi        string    `gorm:"not null" json:"alamat_provinsi"`
	AlamatKabupatenKota   string    `gorm:"not null" json:"alamat_kabupatenkota"`
	AlamatKecamatan       string    `gorm:"not null" json:"alamat_kecamatan"`
	AlamatKelurahanDesa   string    `gorm:"not null" json:"alamat_kelurahandesa"`
	PendataanTanggal      time.Time `gorm:"type:date;not null" json:"pendataan_tanggal"`
	SambungDesa           string    `gorm:"not null" json:"sambung_desa"`
	SambungKelompok       string    `gorm:"not null" json:"sambung_kelompok"`
	Hobi                  HobiMap   `gorm:"type:jsonb" json:"hobi"`
	SekolahKelas          string    `gorm:"not null" json:"sekolah_kelas"`
	NomorHape             *string   `json:"nomor_hape"`
	NamaAyah              string    `gorm:"not null" json:"nama_ayah"`
	NamaIbu               string    `gorm:"not null" json:"nama_ibu"`
	StatusAyah            string    `gorm:"not null" json:"status_ayah"`
	StatusIbu             string    `gorm:"not null" json:"status_ibu"`
	NomorHapeAyah         *string   `json:"nomor_hape_ayah"`
	NomorHapeIbu          *string   `json:"nomor_hape_ibu"`
	JenisKelamin          string    `gorm:"not null" json:"jenis_kelamin"`
	Daerah                string    `gorm:"not null;index" json:"daerah"`
	FotoFilename          *string   `json:"foto_filename"`
	FotoID                *string   `json:"foto_id"`
	CreatedAt             string    `gorm:"not null" json:"created_at"`
}

// TableName returns the table name for the BiodataGenerus model
func (BiodataGenerus) TableName() string {
	return "data_biodata_generus"
}

// BeforeCreate generates a biodata ID before creating the record
func (b *BiodataGenerus) BeforeCreate(tx *gorm.DB) error {
	if b.BiodataID == "" {
		b.BiodataID = generateBiodataID(6)
	}
	if b.CreatedAt == "" {
		b.CreatedAt = time.Now().Format("2006-01-02")
	}
	return nil
}

// generateBiodataID generates a 6-character alphanumeric ID
func generateBiodataID(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"
	b := make([]byte, length)
	
	// Ensure first character is not 0 for better readability
	firstCharset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"
	randomBytes := make([]byte, 1)
	rand.Read(randomBytes)
	b[0] = firstCharset[randomBytes[0]%byte(len(firstCharset))]
	
	// Generate remaining characters
	for i := 1; i < length; i++ {
		rand.Read(randomBytes)
		b[i] = charset[randomBytes[0]%byte(len(charset))]
	}
	return string(b)
}

// BiodataGenerusResponse represents the response structure for biodata operations
type BiodataGenerusResponse struct {
	ID            uint      `json:"id"`
	BiodataID     string    `json:"biodata_id"`
	NamaLengkap   string    `json:"nama_lengkap"`
	NamaPanggilan string    `json:"nama_panggilan"`
	SambungDesa   string    `json:"sambung_desa"`
	SambungKelompok string  `json:"sambung_kelompok"`
	JenisKelamin  string    `json:"jenis_kelamin"`
	FotoFilename  *string   `json:"foto_filename"`
	FotoID        *string   `json:"foto_id"`
	CreatedAt     string    `json:"created_at"`
}

// BiodataGenerusGetResponse represents the list response structure
type BiodataGenerusGetResponse struct {
	BiodataID       string `json:"biodata_id"`
	NamaLengkap     string `json:"nama_lengkap"`
	NamaPanggilan   string `json:"nama_panggilan"`
	SambungDesa     string `json:"sambung_desa"`
	SambungKelompok string `json:"sambung_kelompok"`
	JenisKelamin    string `json:"jenis_kelamin"`
}

// BiodataGenerusDetailResponse represents the detailed response structure
type BiodataGenerusDetailResponse struct {
	BiodataID             string    `json:"biodata_id"`
	NamaLengkap           string    `json:"nama_lengkap"`
	NamaPanggilan         string    `json:"nama_panggilan"`
	KelahiranTempat       string    `json:"kelahiran_tempat"`
	KelahiranTanggal      time.Time `json:"kelahiran_tanggal"`
	AlamatJalan           string    `json:"alamat_jalan"`
	AlamatNomor           *string   `json:"alamat_nomor"`
	AlamatProvinsi        string    `json:"alamat_provinsi"`
	AlamatKabupatenKota   string    `json:"alamat_kabupatenkota"`
	AlamatKecamatan       string    `json:"alamat_kecamatan"`
	AlamatKelurahanDesa   string    `json:"alamat_kelurahandesa"`
	PendataanTanggal      time.Time `json:"pendataan_tanggal"`
	SambungDesa           string    `json:"sambung_desa"`
	SambungKelompok       string    `json:"sambung_kelompok"`
	Hobi                  HobiMap   `json:"hobi"`
	SekolahKelas          string    `json:"sekolah_kelas"`
	NomorHape             *string   `json:"nomor_hape"`
	NamaAyah              string    `json:"nama_ayah"`
	NamaIbu               string    `json:"nama_ibu"`
	StatusAyah            string    `json:"status_ayah"`
	StatusIbu             string    `json:"status_ibu"`
	NomorHapeAyah         *string   `json:"nomor_hape_ayah"`
	NomorHapeIbu          *string   `json:"nomor_hape_ibu"`
	JenisKelamin          string    `json:"jenis_kelamin"`
	Daerah                string    `json:"daerah"`
	FotoFilename          *string   `json:"foto_filename"`
	FotoID                *string   `json:"foto_id"`
	CreatedAt             string    `json:"created_at"`
}
