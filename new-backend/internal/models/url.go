package models

import (
	"crypto/rand"

	"gorm.io/gorm"
)

// URL represents the shortened URL model
// Maps to table: rec_shorten_urls
type URL struct {
	ID      uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	URL     string `gorm:"not null" json:"url"`
	URLCode string `gorm:"unique;not null;size:10" json:"url_code"`
}

// TableName returns the table name for the URL model
func (URL) TableName() string {
	return "rec_shorten_urls"
}

// BeforeCreate generates a URL code before creating the record
func (u *URL) BeforeCreate(tx *gorm.DB) error {
	if u.URLCode == "" {
		u.URLCode = generateURLCode(6)
	}
	return nil
}

// generateURLCode generates a random alphanumeric code
func generateURLCode(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		randomBytes := make([]byte, 1)
		rand.Read(randomBytes)
		b[i] = charset[randomBytes[0]%byte(len(charset))]
	}
	return string(b)
}

// URLResponse represents the response structure for URL operations
type URLResponse struct {
	URL     string `json:"url"`
	URLCode string `json:"url_code"`
}

// URLCreate represents the request structure for creating URLs
type URLCreate struct {
	URL string `json:"url" form:"url" binding:"required"`
}
