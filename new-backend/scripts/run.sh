#!/bin/bash

# Run script for the Go backend
set -e

echo "Starting GNRS Go Backend..."

# Set default environment variables if not set
export PORT=${PORT:-8000}
export ENVIRONMENT=${ENVIRONMENT:-development}

# Database configuration
export POSTGRES_CONTAINER_NAME=${POSTGRES_CONTAINER_NAME:-localhost}
export POSTGRES_USER=${POSTGRES_USER:-besb_user}
export POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-NsJTxYB5VY7hTN3EAulY1Ice132qKhgH}
export POSTGRES_DB=${POSTGRES_DB:-besb_db}
export POSTGRES_PORT=${POSTGRES_PORT:-5432}

# Redis configuration
export REDIS_CONTAINER_NAME=${REDIS_CONTAINER_NAME:-localhost}
export REDIS_PORT=${REDIS_PORT:-6379}

# JWT configuration
export JWT_SECRET=${JWT_SECRET:-Pxf0AsnFeejnpZfp4Ya8F4wsyJcqSV2Q}

# CORS configuration
export CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-"https://ngaji.brkh.work,https://29.brkh.work,https://hj.brkh.work,https://hd.brkh.work"}

# Create fotos directory if it doesn't exist
mkdir -p fotos

# Run the application
go run cmd/api/main.go
