#!/bin/bash

# Basic API test script for GNRS Go Backend
set -e

BASE_URL="http://localhost:8000"

echo "Testing GNRS Go Backend API..."

# Test health endpoint
echo "1. Testing health endpoint..."
curl -s "$BASE_URL/" | jq .

echo -e "\n2. Testing health check..."
curl -s "$BASE_URL/health" | jq .

# Test wilayah endpoints (no auth required)
echo -e "\n3. Testing wilayah provinces..."
curl -s "$BASE_URL/wilayah/provinces" | jq '. | length'

# Test data endpoints (no auth required)
echo -e "\n4. Testing data/hobi..."
curl -s "$BASE_URL/data/hobi" | jq '. | length'

echo -e "\n5. Testing data/kelas-sekolah..."
curl -s "$BASE_URL/data/kelas-sekolah" | jq '. | length'

# Test URL shortener GET (no auth required)
echo -e "\n6. Testing URL shortener GET (should return 404)..."
curl -s -w "%{http_code}" "$BASE_URL/url/nonexistent" | tail -c 3

echo -e "\n\nBasic API tests completed!"
echo "Note: Authentication and write operations require proper setup of database and Redis."
