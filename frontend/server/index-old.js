// Cloudflare Workers as API proxy
const API_HOST = "api.var.my.id";
export default {
  async fetch(request) {
    const url = new URL(request.url);
    if (url.pathname.startsWith("/api/")) {
      const targetPath = url.pathname.replace(/^\/api\//, "/");
      const targetUrl = `https://${API_HOST}${targetPath}${url.search}`;
      return fetch(targetUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: "follow",
      });
    }
    return fetch(request);
  },
};
