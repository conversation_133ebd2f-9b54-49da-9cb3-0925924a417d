<template>
  <div id="app">
    <h1>Pantauan Biodata Generus</h1>
    <h2 v-if="daerah">{{ daerah }}</h2>

    <FilterSection
      :filters="filters"
      :unique-desa="uniqueDesa"
      :unique-kelompok="uniqueKelompok"
      @filter-change="handleFilterChange"
    />

    <StatisticsSection :stats="filteredStats" />

    <BiodataTable
      :data="filteredData"
      :sort-key="sortKey"
      :sort-order="sortOrder"
      @sort="sortTable"
      @row-click="openDetailModal"
    />

    <BiodataDetailModal
      :show="showModal"
      :data="selectedDetailData"
      :api-key="apiKey"
      @close="closeModal"
    />
  </div>
</template>

<script>
import FilterSection from "./components/FilterSection.vue";
import StatisticsSection from "./components/StatisticsSection.vue";
import BiodataTable from "./components/BiodataTable.vue";
import BiodataDetailModal from "./components/BiodataDetailModal.vue";

export default {
  components: {
    FilterSection,
    StatisticsSection,
    BiodataTable,
    BiodataDetailModal,
  },
  data() {
    return {
      biodataData: [],
      detailedData: {},
      showModal: false,
      selectedDetailData: null,
      filters: {
        sambung_desa: "",
        sambung_kelompok: "",
        nama: "",
      },
      apiKey: "",
      daerah: "",
      sortKey: "",
      sortOrder: "asc",
    };
  },
  computed: {
    uniqueDesa() {
      return [
        ...new Set(this.biodataData.map((item) => item.sambung_desa)),
      ].sort();
    },
    uniqueKelompok() {
      let data = this.biodataData;
      if (this.filters.sambung_desa) {
        data = data.filter(
          (item) => item.sambung_desa === this.filters.sambung_desa,
        );
      }
      return [...new Set(data.map((item) => item.sambung_kelompok))].sort();
    },
    filteredData() {
      const filtered = this.biodataData.filter((item) => {
        const desaMatch =
          !this.filters.sambung_desa ||
          item.sambung_desa === this.filters.sambung_desa;
        const kelompokMatch =
          !this.filters.sambung_kelompok ||
          item.sambung_kelompok === this.filters.sambung_kelompok;
        const namaMatch =
          !this.filters.nama ||
          item.nama_lengkap
            .toLowerCase()
            .includes(this.filters.nama.toLowerCase()) ||
          item.nama_panggilan
            .toLowerCase()
            .includes(this.filters.nama.toLowerCase());
        return desaMatch && kelompokMatch && namaMatch;
      });

      if (this.sortKey) {
        filtered.sort((a, b) => {
          let aVal = this.sortKey === "index" ? 1 : a[this.sortKey];
          let bVal = this.sortKey === "index" ? 1 : b[this.sortKey];

          if (typeof aVal === "string") aVal = aVal.toLowerCase();
          if (typeof bVal === "string") bVal = bVal.toLowerCase();

          if (aVal < bVal) return this.sortOrder === "asc" ? -1 : 1;
          if (aVal > bVal) return this.sortOrder === "asc" ? 1 : -1;
          return 0;
        });
      }

      return filtered;
    },
    filteredStats() {
      const filtered = this.filteredData;
      let with_photo_count = 0;
      let without_photo_count = 0;

      // Calculate photo stats based on the filtered data
      filtered.forEach((item) => {
        const detail = this.detailedData[item.biodata_id];
        if (detail) {
          if (detail.foto_filename) {
            with_photo_count++;
          } else {
            without_photo_count++;
          }
        } else {
          // Assume unknown photo status as 'without photo'
          without_photo_count++;
        }
      });

      return {
        total_count: filtered.length,
        male_count: filtered.filter(
          (item) => item.jenis_kelamin.toLowerCase() === "laki-laki",
        ).length,
        female_count: filtered.filter(
          (item) => item.jenis_kelamin.toLowerCase() === "perempuan",
        ).length,
        with_photo_count: with_photo_count,
        without_photo_count: without_photo_count,
      };
    },
  },
  methods: {
    handleFilterChange(newFilters) {
      // If desa is changed, reset kelompok
      if (newFilters.sambung_desa !== this.filters.sambung_desa) {
        newFilters.sambung_kelompok = "";
      }
      this.filters = { ...newFilters };
    },
    async fetchBiodataGenerus() {
      let apiUrl = "/api/biodata/generus/";
      if (this.daerah) {
        apiUrl += `?daerah=${this.daerah}`;
      }

      try {
        const response = await fetch(apiUrl, {
          headers: { Authorization: `ApiKey ${this.apiKey}` },
        });
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        this.biodataData = await response.json();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    },
    async fetchDetailedData(biodataId) {
      console.log(
        "🔍 [FETCH DEBUG] Fetching detailed data for biodata ID:",
        biodataId,
      );

      if (this.detailedData[biodataId]) {
        console.log(
          "📋 [FETCH DEBUG] Data already cached, returning cached data",
        );
        return this.detailedData[biodataId]; // Already fetched
      }

      const apiUrl = `/api/biodata/generus/${biodataId}`;
      console.log("🌐 [FETCH DEBUG] API URL:", apiUrl);
      console.log(
        "🔑 [FETCH DEBUG] API Key (first 10 chars):",
        this.apiKey ? this.apiKey.substring(0, 10) + "..." : "NOT PROVIDED",
      );

      try {
        console.log("🚀 [FETCH DEBUG] Making fetch request...");
        const response = await fetch(apiUrl, {
          headers: { Authorization: `ApiKey ${this.apiKey}` },
        });

        console.log(
          "📡 [FETCH DEBUG] Response received, status:",
          response.status,
        );

        if (!response.ok) {
          console.error(
            "❌ [FETCH DEBUG] Response not OK, status:",
            response.status,
          );
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("📋 [FETCH DEBUG] Data parsed successfully:", data);
        console.log(
          "📸 [FETCH DEBUG] Photo filename in response:",
          data.foto_filename,
        );

        // Use direct assignment for Vue 3 reactivity
        this.detailedData[biodataId] = data;
        return data;
      } catch (error) {
        console.error("💥 [FETCH DEBUG] Error fetching detailed data:", error);
        return null;
      }
    },
    async openDetailModal(biodataId) {
      console.log(
        "🔍 [MODAL DEBUG] Opening detail modal for biodata ID:",
        biodataId,
      );
      this.showModal = true;
      this.selectedDetailData = null; // Show loading

      const data = await this.fetchDetailedData(biodataId);
      if (data) {
        console.log("📋 [MODAL DEBUG] Detail data received:", data);
        console.log("📸 [MODAL DEBUG] Photo filename:", data.foto_filename);
        console.log("🔑 [MODAL DEBUG] API Key available:", !!this.apiKey);
        this.selectedDetailData = data;
      } else {
        console.error(
          "❌ [MODAL DEBUG] No data received for biodata ID:",
          biodataId,
        );
      }
    },
    closeModal() {
      this.showModal = false;
      this.selectedDetailData = null;
    },

    sortTable(key) {
      if (this.sortKey === key) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortKey = key;
        this.sortOrder = "asc";
      }
    },
  },
  mounted() {
    const params = new URLSearchParams(window.location.search);
    this.apiKey = params.get("key");
    this.daerah = params.get("daerah") || "";
    this.fetchBiodataGenerus();
    document.title = `PANTAU BIODATA GENERUS ${this.daerah}`.trim();
  },
};
</script>

<style>
body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

h1 {
  font-size: 2.5em;
  text-align: center;
  margin-bottom: 20px;
}

button {
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  appearance: none;
  border-radius: 20px;
  box-sizing: border-box;
  width: auto;
  margin: 20px 10px;
  padding: 0 20px;
  display: inline-block;
  cursor: pointer;
  height: 50px;
}

.button-container {
  text-align: center;
  margin-top: 20px;
  width: 100%;
  max-width: 550px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  button {
    width: 100%;
  }
}

.modal-header h3 {
  margin: 0;
  color: #2e5a35;
  font-size: 20px;
}

.close-button {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #2e5a35;
}

.modal-body {
  padding: 25px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.detail-section {
  background-color: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #2e5a35;
  font-size: 16px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.detail-section strong {
  color: #495057;
}

.photo-section {
  text-align: center;
}

.biodata-photo {
  max-width: 200px;
  max-height: 250px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.no-photo {
  color: #6c757d;
  font-style: italic;
  margin-top: 10px;
}

.loading-content {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}
</style>
