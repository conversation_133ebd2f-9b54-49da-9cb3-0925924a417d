export default {
  methods: {
    async fetchDataFromApi(
      url,
      processDataFn,
      errorMessage,
      retryFn,
      stateObj,
    ) {
      console.log(`Fetching data from ${url}...`);
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const data = await response.json();

        // Process data with custom function if provided
        const processedData = processDataFn ? processDataFn(data) : data;

        // Update state based on stateObj reference
        stateObj.options = processedData;
        stateObj.isLoading = false;
        stateObj.dataLoaded = true;
        stateObj.loadError = null;

        console.log(`Data loaded successfully from ${url}`);
        return processedData;
      } catch (error) {
        console.error(`Error fetching data from ${url}:`, error);
        stateObj.loadError =
          errorMessage || "Gagal memuat data. Silakan muat ulang halaman.";
        stateObj.isLoading = false;
        stateObj.dataLoaded = false;

        // Retry after delay if retry function provided
        if (retryFn) {
          setTimeout(retryFn, 5000);
        }
        return null;
      }
    },

    async fetchKelompokData(dataParam) {
      if (!dataParam) {
        console.error("Parameter data tidak ditemukan");
        this.kelompokData.loadError = "Parameter data tidak ditemukan";
        this.kelompokData.isLoading = false;
        return;
      }

      const encodedParam = encodeURIComponent(dataParam);
      const url = `/api/data/daerah/${encodedParam}/`;

      const processData = (data) => {
        const formattedData = {};
        let preselectedItem = null;

        // Format the data into a nested structure (ranah -> detail_ranah)
        data.forEach((item) => {
          // Map ranah to desa and detail_ranah to kelompok
          if (!formattedData[item.ranah]) {
            formattedData[item.ranah] = [];
          }
          formattedData[item.ranah].push(item.detail_ranah);

          // Check if this item matches our URL parameter
          if (
            dataParam &&
            (item.ranah.toLowerCase().includes(dataParam.toLowerCase()) ||
              item.detail_ranah.toLowerCase().includes(dataParam.toLowerCase()))
          ) {
            preselectedItem = {
              desa: item.ranah,
              kelompok: item.detail_ranah,
            };
          }
        });

        // If we found a match for the URL parameter, pre-select it
        if (preselectedItem) {
          setTimeout(() => {
            this.formData.sambung_desa = preselectedItem.desa;
            this.formData.sambung_kelompok = preselectedItem.kelompok;
          }, 0);
        }

        return formattedData;
      };

      await this.fetchDataFromApi(
        url,
        processData,
        "Gagal memuat data kelompok. Silakan muat ulang halaman.",
        () => this.fetchKelompokData(dataParam),
        this.kelompokData,
      );
    },

    async fetchHobiData() {
      await this.fetchDataFromApi(
        "/api/data/hobi",
        null,
        "Gagal memuat data hobi. Silakan muat ulang halaman.",
        () => this.fetchHobiData(),
        this.hobiData,
      );
    },

    async fetchSekolahKelasData() {
      try {
        const response = await fetch(
          "/api/data/kelas-sekolah",
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        this.sekolahKelasOptions = data;
      } catch (error) {
        console.error("Error fetching sekolah/kelas data:", error);
      }
    },

    validateFormFields() {
      const { sambung_kelompok, sambung_desa } = this.formData;
      if (!sambung_kelompok || !sambung_desa) {
        alert(
          "Pilihan Desa & Kelompok wajib diisi. Silakan ketik dan pilih dari daftar yang muncul.",
        );
        return false;
      }

      const isValidKelompok = this.flattenedKelompok.some(
        (item) =>
          item.kelompok === sambung_kelompok && item.desa === sambung_desa,
      );

      if (!isValidKelompok) {
        alert(
          "Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik",
        );
        return false;
      }
      return true;
    },

    showReviewData() {
      if (!this.validateFormFields()) {
        return;
      }
      this.showReview = true;
    },

    editForm(data) {
      // Explicitly log the alamat_tinggal before toggling to help debug
      console.log("EditForm called with alamat_tinggal:", this.formData.alamat_tinggal);
      
      // Save current address if available
      const savedAddress = data && data.savedAddress ? data.savedAddress : this.formData.alamat_tinggal;
      
      // Toggle the review state
      this.showReview = false;
      
      // Force a refresh of the component tree and restore address if needed
      this.$nextTick(() => {
          if (savedAddress && (!this.formData.alamat_tinggal || this.formData.alamat_tinggal !== savedAddress)) {
              console.log("Restoring alamat_tinggal to:", savedAddress);
              this.formData.alamat_tinggal = savedAddress;
          }
          console.log("After toggling, alamat_tinggal is:", this.formData.alamat_tinggal);
      });
    },

    async submitToAPI() {
      if (this.isSubmitting) return;

      console.log("=== SUBMISSION DEBUG START ===");
      console.log("API Key present:", !!this.apiKey);

      // Check for required fields again before submission
      if (!this.formData.sambung_desa || !this.formData.sambung_kelompok) {
        console.error("Missing required Desa/Kelompok fields");
        alert("Pilihan Desa & Kelompok wajib diisi sebelum mengirim data.");
        return;
      }

      // Check for API key
      if (!this.apiKey) {
        console.error("API key missing, aborting submission");
        alert("API key diperlukan. Silakan gunakan URL dengan parameter key.");
        this.showApiKeyError = true;
        return;
      }

      this.isSubmitting = true;
      console.log("Starting form submission process");
      console.log("Original form data:", JSON.stringify(this.formData));
      console.log("Selected hobi items:", this.selectedHobi);
      console.log("CRITICAL FIELDS CHECK:");
      console.log("sambung_desa:", this.formData.sambung_desa);
      console.log("sambung_kelompok:", this.formData.sambung_kelompok);

      try {
        const formData = new FormData();
        console.log("Created new FormData object");

        // Add the critical sambung fields first to ensure they're included
        formData.append("sambung_desa", this.formData.sambung_desa);
        formData.append("sambung_kelompok", this.formData.sambung_kelompok);
        console.log("Added sambung fields to FormData");

        // Process hobi data as a properly formatted JSON string
        if (this.selectedHobi.length > 0) {
          console.log(
            "Processing hobi data, found",
            this.selectedHobi.length,
            "selected items",
          );

          // Group hobi items by category
          const groupedHobi = {};
          this.selectedHobi.forEach((item) => {
            if (!groupedHobi[item.kategori]) {
              groupedHobi[item.kategori] = [];
            }
            groupedHobi[item.kategori].push(item.hobi);
          });

          // Convert arrays to strings for API compatibility
          const hobiObj = {};
          for (const [kategori, hobiList] of Object.entries(groupedHobi)) {
            hobiObj[kategori] = hobiList.join(", ");
          }

          // Convert to JSON string as required by backend
          const hobiJson = JSON.stringify(hobiObj);
          formData.append("hobi", hobiJson);
          console.log("Added hobi JSON string to FormData");
        } else {
          formData.append("hobi", JSON.stringify({}));
          console.log("No hobi selected, adding empty JSON object");
        }

        // Add all other form fields to the FormData object
        console.log("Adding other form fields to FormData");
        for (const key in this.formData) {
          // Skip already handled fields
          if (["hobi", "sambung_desa", "sambung_kelompok"].includes(key)) {
            continue;
          }

          // Only add non-empty values for optional fields
          if (
            this.formData[key] !== null &&
            this.formData[key] !== undefined &&
            this.formData[key] !== ""
          ) {
            formData.append(key, this.formData[key]);
          }
        }

        // Send data to the API endpoint with Authorization header
        const apiUrl = "/api/biodata/generus";
        console.log("Sending request to:", apiUrl);

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            Authorization: "ApiKey " + this.apiKey,
          },
          body: formData,
        });

        console.log("Response status:", response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Error response from server:", errorText);
          try {
            const errorJson = JSON.parse(errorText);
            console.error("Parsed error response:", errorJson);
          } catch (e) {
            console.log("Error response is not valid JSON");
          }
          throw new Error(`Server error: ${response.status} - ${errorText}`);
        }

        const responseData = await response.json();
        console.log("Success response:", responseData);

        // Show success message
        this.showReview = false;
        this.showSuccess = true;
        console.log("Form submitted successfully");
      } catch (error) {
        console.error("Error in submission process:", error);
        console.error("Error stack:", error.stack);
        alert("Terjadi kesalahan saat mengirim data. Mohon coba lagi.");
      } finally {
        this.isSubmitting = false;
        console.log("=== SUBMISSION DEBUG END ===");
      }
    },

    resetForm() {
      // Reload the page to completely reset all form data and state
      window.location.reload();
    },

    getCurrentDate() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    formatDate(dateString) {
      if (!dateString) return "";
      const options = { day: "numeric", month: "long", year: "numeric" };
      return new Date(dateString).toLocaleDateString("id-ID", options);
    },

    handleKelompokInputChange() {
      // Retry fetching data if needed
      if (!this.kelompokData.dataLoaded && !this.kelompokData.isLoading) {
        console.log("Data not loaded, retrying fetch...");
        const dataParam = new URLSearchParams(window.location.search).get(
          "data",
        );
        this.fetchKelompokData(dataParam);
      }
    },
  },
};
