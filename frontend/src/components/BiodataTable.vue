<template>
  <div class="table-container">
    <table id="biodataTable">
      <thead>
        <tr>
          <th>No.</th>
          <th @click="$emit('sort', 'nama_lengkap')">
            <PERSON><PERSON>
            <span v-if="sortKey === 'nama_lengkap'">{{
              sortOrder === "asc" ? "↑" : "↓"
            }}</span>
          </th>
          <th @click="$emit('sort', 'nama_panggilan')">
            <PERSON>a <PERSON>
            <span v-if="sortKey === 'nama_panggilan'">{{
              sortOrder === "asc" ? "↑" : "↓"
            }}</span>
          </th>
          <th @click="$emit('sort', 'sambung_desa')">
            Desa
            <span v-if="sortKey === 'sambung_desa'">{{
              sortOrder === "asc" ? "↑" : "↓"
            }}</span>
          </th>
          <th @click="$emit('sort', 'sambung_kelompok')">
            Kelompok
            <span v-if="sortKey === 'sambung_kelompok'">{{
              sortOrder === "asc" ? "↑" : "↓"
            }}</span>
          </th>
          <th @click="$emit('sort', 'jenis_kelamin')">
            Jenis Kelamin
            <span v-if="sortKey === 'jenis_kelamin'">{{
              sortOrder === "asc" ? "↑" : "↓"
            }}</span>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr 
          v-for="(item, index) in data" 
          :key="index" 
          @click="$emit('row-click', item.biodata_id)" 
          class="clickable-row"
        >
          <td>{{ index + 1 }}</td>
          <td>{{ item.nama_lengkap }}</td>
          <td>{{ item.nama_panggilan }}</td>
          <td>{{ item.sambung_desa }}</td>
          <td>{{ item.sambung_kelompok }}</td>
          <td>{{ item.jenis_kelamin }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: "BiodataTable",
  props: {
    data: {
      type: Array,
      required: true,
    },
    sortKey: {
      type: String,
      default: "",
    },
    sortOrder: {
      type: String,
      default: "asc",
    },
  },
  emits: ["sort", "row-click"],
};
</script>

<style scoped>
.table-container {
  width: 100%;
  max-width: 768px;
  overflow-x: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  margin: 0 auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  min-width: 100%;
}

th,
td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  white-space: nowrap;
}

th {
  background-color: #f4f4f4;
  cursor: pointer;
  position: relative;
  user-select: none;
  transition: background-color 0.2s;
}

th:hover {
  background-color: #e0e0e0;
}

th span {
  margin-left: 5px;
  color: #666;
}

th:first-child,
th:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 20;
}

td:first-child,
td:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 10;
}

tr:last-child td {
  border-bottom: none;
}

th:first-child {
  cursor: default;
}

th:first-child:hover {
  background-color: #f4f4f4;
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.clickable-row:hover {
  background-color: #f8f9fa;
}
</style>
