<template>
  <div>
    <div class="form-row">
      <label for="jalan">Jalan :</label>
      <input
        id="jalan"
        type="text"
        ref="jalanInputEl"
        :value="jalan"
        @input="handleJalanInput"
        @keydown="handleJalanKeydown"
        @change="persistJalanChange($event)"
        placeholder="<PERSON><PERSON> Jalan, Gang, dan RT/RW (jika ada)."
        required
      />
    </div>
    <div class="form-row">
      <label for="nomor">No. :</label>
      <input
        id="nomor"
        type="text"
        ref="nomorInputEl"
        :value="nomor"
        @input="handleNomorInput"
        @keydown="handleNomorKeydown"
        @change="persistNomorChange($event)"
        placeholder="Nomor Rumah. Tulis 0 jika tidak ada."
        required
      />
    </div>
    <!-- Provinsi input field using SuggestionsMixin -->
    <div class="form-row">
      <label for="provinsi">Provinsi :</label>
      <div class="suggestions-container">
        <input
          id="provinsi"
          ref="inputEl"
          type="text"
          v-model="inputValue"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          @keyup="handleKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleCompositionEnd"
          placeholder="Ketik untuk mencari provinsi"
          required
        />
        <div v-if="isLoadingProvinsi" class="loading-indicator">Loading...</div>
        <div
          class="suggestions"
          v-if="showSuggestions && filteredProvinsiList.length"
        >
          <div
            v-for="item in filteredProvinsiList"
            :key="item.code"
            class="suggestion-item"
            @click="selectProvinsi(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kabupatenList.length > 0">
      <label for="kabupaten">Kota / Kab. :</label>
      <div class="suggestions-container">
        <input
          id="kabupaten"
          ref="kabupatenInputEl"
          type="text"
          v-model="kabupatenInput"
          @input="handleKabupatenInput($event)"
          @focus="handleKabupatenFocus"
          @blur="handleKabupatenBlur"
          @keyup="handleKabupatenKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKabupatenCompositionEnd"
          placeholder="Ketik untuk mencari kabupaten"
          required
        />
        <div v-if="isLoadingKabupaten" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKabupatenSuggestions && filteredKabupatenList.length"
        >
          <div
            v-for="item in filteredKabupatenList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKabupaten(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kecamatanList.length > 0">
      <label for="kecamatan">Kecamatan :</label>
      <div class="suggestions-container">
        <input
          id="kecamatan"
          ref="kecamatanInputEl"
          type="text"
          v-model="kecamatanInput"
          @input="handleKecamatanInput($event)"
          @focus="handleKecamatanFocus"
          @blur="handleKecamatanBlur"
          @keyup="handleKecamatanKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKecamatanCompositionEnd"
          placeholder="Ketik untuk mencari kecamatan"
          required
        />
        <div v-if="isLoadingKecamatan" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKecamatanSuggestions && filteredKecamatanList.length"
        >
          <div
            v-for="item in filteredKecamatanList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKecamatan(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kelurahanList.length > 0">
      <label for="kelurahan">Desa / Kel. :</label>
      <div class="suggestions-container">
        <input
          id="kelurahan"
          ref="kelurahanInputEl"
          type="text"
          v-model="kelurahanInput"
          @input="handleKelurahanInput($event)"
          @focus="handleKelurahanFocus"
          @blur="handleKelurahanBlur"
          @keyup="handleKelurahanKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKelurahanCompositionEnd"
          placeholder="Ketik untuk mencari kelurahan/desa"
          required
        />
        <div v-if="isLoadingKelurahan" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKelurahanSuggestions && filteredKelurahanList.length"
        >
          <div
            v-for="item in filteredKelurahanList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKelurahan(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SuggestionsMixin from "../mixins/SuggestionsMixin";

export default {
  mixins: [SuggestionsMixin],
  props: {
    value: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      jalan: "",
      nomor: "",
      provinsiList: [],
      kabupatenList: [],
      kecamatanList: [],
      kelurahanList: [],

      selectedProvinsi: null,
      selectedKabupaten: null,
      selectedKecamatan: null,
      selectedKelurahan: null,

      // For inputs not handled by mixin
      kabupatenInput: "",
      kecamatanInput: "",
      kelurahanInput: "",

      showKabupatenSuggestions: false,
      showKecamatanSuggestions: false,
      showKelurahanSuggestions: false,

      isLoadingProvinsi: false,
      isLoadingKabupaten: false,
      isLoadingKecamatan: false,
      isLoadingKelurahan: false,

      // Add an update lock flag to prevent circular updates
      isUpdating: false,

      // Add debounce timer
      updateTimer: null,

      // Cache for API responses to improve performance
      wilayahCache: new Map(),

      // Debounce timers for input handling
      inputDebounceTimers: {
        kabupaten: null,
        kecamatan: null,
        kelurahan: null,
      },

      // Add flags to track if an item has been selected
      provinsiSelected: false,
      kabupatenSelected: false,
      kecamatanSelected: false,
      kelurahanSelected: false,

      // Store original address state to detect changes
      originalAddressState: null,

      // Add a property to store the last emitted address for comparison
      lastEmittedAddress: "",

      // Add flags to track user editing state
      isUserEditing: false,
      userEditingTimer: null,

      // Add permanent persistence for address components
      persistedJalan: "",
      persistedNomor: "",
    };
  },
  computed: {
    filteredProvinsiList() {
      const searchTerm = this.inputValue.toLowerCase(); // From SuggestionsMixin
      if (!searchTerm) return [];
      return this.provinsiList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKabupatenList() {
      const searchTerm = this.kabupatenInput.toLowerCase();
      if (!searchTerm) return [];
      return this.kabupatenList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKecamatanList() {
      const searchTerm = this.kecamatanInput.toLowerCase();
      if (!searchTerm) return [];
      return this.kecamatanList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKelurahanList() {
      const searchTerm = this.kelurahanInput.toLowerCase();
      if (!searchTerm) return [];
      return this.kelurahanList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
  },
  watch: {
    jalan: "updateAlamat",
    nomor: "updateAlamat",
    selectedProvinsi: "updateAlamat",
    selectedKabupaten: "updateAlamat",
    selectedKecamatan: "updateAlamat",
    selectedKelurahan: "updateAlamat",
    value: {
      immediate: true,
      handler(newVal) {
        console.log("AlamatTinggalForm value changed:", newVal);
        // Only parse if not currently updating and value is valid
        if (
          !this.isUpdating &&
          newVal &&
          typeof newVal === "string" &&
          newVal.trim() !== ""
        ) {
          this.parseExistingAddress(newVal);
        }
      },
    },
    // This is the correct place for watchers, not in methods
    "$parent.isActive": function (isActive) {
      if (!isActive) {
        // Leaving this component - force an address update
        this.forceAddressUpdate();
      }
    },
  },
  methods: {
    // Add keydown handlers to prevent cursor jumps on space
    handleJalanKeydown(event) {
      // Stop propagation for all key events to prevent parent components from capturing them
      event.stopPropagation();

      // Specifically handle space key to prevent focus issues
      if (event.key === " " || event.keyCode === 32) {
        // Don't let the space event bubble up which might cause unwanted behaviors
        event.stopPropagation();

        // Let the default behavior happen (typing a space) but prevent any parent handling
        // Don't call preventDefault() as that would prevent the space from being typed
      }
    },

    handleNomorKeydown(event) {
      // Same handling as jalan keydown
      event.stopPropagation();

      if (event.key === " " || event.keyCode === 32) {
        event.stopPropagation();
      }
    },

    // Improve the input handlers to better maintain focus
    handleJalanInput(event) {
      // Prevent any event bubbling
      event.stopPropagation();

      // Save cursor position
      const cursorPosition = event.target.selectionStart;
      const scrollPosition = event.target.scrollTop;

      // Store the active element to ensure we maintain focus
      const activeElement = document.activeElement;

      // Update the jalan value directly and in persisted storage
      this.jalan = event.target.value;
      this.persistedJalan = event.target.value;

      // Update the global storage too
      try {
        localStorage.setItem("alamat_jalan_temp", event.target.value);
      } catch (e) {
        console.warn("Could not save temp jalan", e);
      }

      // Store the value in our state tracker immediately
      if (this.originalAddressState) {
        this.originalAddressState.jalan = event.target.value;
      }

      // Set a flag to prevent reparsing of address while editing
      this.isUserEditing = true;
      clearTimeout(this.userEditingTimer);

      // Restore cursor position and focus after Vue redraws the DOM
      this.$nextTick(() => {
        // Ensure the same element maintains focus
        if (activeElement === this.$refs.jalanInputEl) {
          this.$refs.jalanInputEl.focus();
          this.$refs.jalanInputEl.setSelectionRange(
            cursorPosition,
            cursorPosition,
          );
          this.$refs.jalanInputEl.scrollTop = scrollPosition;
        }

        // After a longer delay, allow reparsing again
        this.userEditingTimer = setTimeout(() => {
          this.isUserEditing = false;
        }, 1000);
      });
    },

    handleNomorInput(event) {
      // Prevent any event bubbling
      event.stopPropagation();

      const cursorPosition = event.target.selectionStart;
      const scrollPosition = event.target.scrollTop;
      const activeElement = document.activeElement;

      // Update both regular and persisted state with higher priority
      const newNomor = event.target.value;
      this.nomor = newNomor;
      this.persistedNomor = newNomor;

      // Extra: Check if this is a "real" input event vs. programmatic
      const isRealUserInput = event.isTrusted !== false;

      // If it's a real user input, give it higher priority
      if (isRealUserInput) {
        // Save to all possible storage locations
        try {
          localStorage.setItem("alamat_nomor", newNomor);
          localStorage.setItem("alamat_nomor_priority", "true");

          // Also save to session storage for extra redundancy
          sessionStorage.setItem("alamat_nomor_current", newNomor);
        } catch (e) {
          console.warn("Could not save temp nomor", e);
        }
      }

      // Update the global storage too
      try {
        localStorage.setItem("alamat_nomor_temp", event.target.value);
      } catch (e) {
        console.warn("Could not save temp nomor", e);
      }

      // Store the value in our state tracker immediately
      if (this.originalAddressState) {
        this.originalAddressState.nomor = event.target.value;
      }

      this.isUserEditing = true;
      clearTimeout(this.userEditingTimer);

      this.$nextTick(() => {
        if (activeElement === this.$refs.nomorInputEl) {
          this.$refs.nomorInputEl.focus();
          this.$refs.nomorInputEl.setSelectionRange(
            cursorPosition,
            cursorPosition,
          );
          this.$refs.nomorInputEl.scrollTop = scrollPosition;
        }

        this.userEditingTimer = setTimeout(() => {
          this.isUserEditing = false;
        }, 1000);
      });
    },

    persistJalanChange(event) {
      // Store in both regular and persisted state
      this.jalan = event.target.value;
      this.persistedJalan = event.target.value;

      // Save to localStorage for cross-navigation persistence
      try {
        localStorage.setItem("alamat_jalan", event.target.value);
      } catch (e) {
        console.warn("Could not save jalan to localStorage", e);
      }

      // Force an immediate address update
      this.isUserEditing = false;
      this.forceAddressUpdate();
    },

    persistNomorChange(event) {
      // Get the new number value
      const newNomor = event.target.value;
      console.log("Persisting house number change:", newNomor);

      // Update all storage locations
      this.nomor = newNomor;
      this.persistedNomor = newNomor;

      // Save to localStorage with higher priority
      try {
        localStorage.setItem("alamat_nomor", newNomor);
        localStorage.setItem("alamat_nomor_priority", "true"); // Mark as explicitly set by user
      } catch (e) {
        console.warn("Could not save nomor to localStorage", e);
      }

      // Force immediate address update without waiting
      this.isUserEditing = false;
      this.isUpdating = false;

      // Force rebuild the full address string
      this.forceAddressUpdateWithNumber(newNomor);
    },

    forceAddressUpdateWithNumber(forcedNumber) {
      console.log("Force updating address with specific number:", forcedNumber);

      // Clear any existing update
      clearTimeout(this.updateTimer);

      // Make sure we have the most current jalan value
      const currentJalan = this.persistedJalan || this.jalan || "";

      // Build address parts with forced number
      const alamatParts = [];

      if (currentJalan.trim() !== "") {
        alamatParts.push(`Jl. ${currentJalan.trim()},`);
      }

      // Always include the forced number if provided
      if (forcedNumber && forcedNumber.trim() !== "") {
        alamatParts.push(`No. ${forcedNumber.trim()},`);
      }

      // Include other address components
      if (this.selectedKelurahan && this.kelurahanSelected) {
        const kelurahanName = this.selectedKelurahan.name.trim();
        alamatParts.push(`Desa/Kel. ${kelurahanName},`);
      }

      if (this.selectedKecamatan && this.kecamatanSelected) {
        alamatParts.push(`Kec. ${this.selectedKecamatan.name},`);
      }

      if (this.selectedKabupaten?.name) {
        alamatParts.push(`${this.selectedKabupaten.name},`);
      }

      if (this.selectedProvinsi?.name) {
        alamatParts.push(`${this.selectedProvinsi.name}`);
      }

      // Only emit if we have parts to emit
      if (alamatParts.length > 0) {
        const alamat = alamatParts.join(" ");
        console.log(
          "Emitting updated alamat with forced number:",
          forcedNumber,
        );

        // Update the last emitted address
        this.lastEmittedAddress = alamat;

        // Emit the updated address
        this.$emit("input", alamat);
      }
    },

    // Improve handleInput for provinsi to better isolate changes
    handleInput(event) {
      // Prevent bubbling to parent handlers
      if (event) {
        event.stopPropagation();
      }

      // If the input value changes from the selected value, reset selection state
      if (
        this.selectedProvinsi &&
        this.inputValue !== this.selectedProvinsi.name
      ) {
        this.provinsiSelected = false;
        this.selectedProvinsi = null;
      }

      // Only show suggestions if we haven't selected an item
      if (!this.provinsiSelected) {
        this.showSuggestions = true;
      }

      this.$emit("input-change");
    },

    // Modify handleFocus to respect selection state
    handleFocus() {
      // Only show suggestions if we haven't selected anything
      if (!this.provinsiSelected && this.inputValue) {
        this.showSuggestions = true;
      }
    },

    // Modify handleBlur to ensure suggestions are hidden
    handleBlur() {
      setTimeout(() => {
        this.showSuggestions = false;
        // Reset input if nothing selected and leaving the field
        if (!this.selectedProvinsi && !this.provinsiSelected) {
          this.inputValue = "";
        }
      }, 150);
    },

    // Cache management methods
    getCacheKey(type, code = null) {
      return code ? `${type}_${code}` : type;
    },

    getCachedData(type, code = null) {
      const key = this.getCacheKey(type, code);
      const cached = this.wilayahCache.get(key);

      if (cached) {
        // Check if cache is still valid (5 minutes)
        const now = Date.now();
        if (now - cached.timestamp < 5 * 60 * 1000) {
          return cached.data;
        } else {
          // Remove expired cache
          this.wilayahCache.delete(key);
        }
      }
      return null;
    },

    setCachedData(type, data, code = null) {
      const key = this.getCacheKey(type, code);
      this.wilayahCache.set(key, {
        data: data,
        timestamp: Date.now(),
      });

      // Limit cache size to prevent memory issues
      if (this.wilayahCache.size > 100) {
        // Remove oldest entries
        const entries = Array.from(this.wilayahCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        for (let i = 0; i < 20; i++) {
          this.wilayahCache.delete(entries[i][0]);
        }
      }
    },

    // Prefetch popular regions for better performance
    async prefetchPopularRegions() {
      // Prefetch Jakarta, Jawa Barat, Jawa Tengah, Jawa Timur (most common provinces)
      const popularProvinceCodes = ["31", "32", "33", "35"]; // Jakarta, Jabar, Jateng, Jatim

      for (const code of popularProvinceCodes) {
        if (!this.getCachedData("regencies", code)) {
          try {
            const response = await fetch(`/api/wilayah/regencies/${code}`);
            if (response.ok) {
              const data = await response.json();
              if (data.data) {
                this.setCachedData("regencies", data.data, code);
              }
            }
          } catch (error) {
            // Silently fail for prefetching
            console.debug("Prefetch failed for province", code, error);
          }
        }
      }
    },

    // Clean up method
    cleanupTimers() {
      clearTimeout(this.updateTimer);
      Object.values(this.inputDebounceTimers).forEach((timer) => {
        if (timer) clearTimeout(timer);
      });
    },

    async fetchProvinsi(retryCount = 0) {
      // Check cache first
      const cachedData = this.getCachedData("provinces");
      if (cachedData) {
        this.provinsiList = cachedData;
        return;
      }

      try {
        this.isLoadingProvinsi = true;

        // Add timeout and retry logic for better reliability
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch("/api/wilayah/provinces", {
          signal: controller.signal,
          headers: {
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          // Handle specific HTTP status codes
          if (response.status === 502 && retryCount < 3) {
            console.warn(
              `502 error fetching provinces, retrying... (attempt ${retryCount + 1})`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchProvinsi(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch provinces: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();

        if (!data.data || !Array.isArray(data.data)) {
          console.error("Invalid provinces data format:", data);
          throw new Error("Invalid data format received");
        }

        this.provinsiList = data.data;

        // Cache the result
        this.setCachedData("provinces", data.data);
      } catch (error) {
        console.error("Error fetching provinces:", error);

        // If it's a network error and we haven't retried too many times, try again
        if (
          (error.name === "AbortError" || error.message.includes("fetch")) &&
          retryCount < 2
        ) {
          console.warn(
            `Network error fetching provinces, retrying... (attempt ${retryCount + 1})`,
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchProvinsi(retryCount + 1);
        }

        this.provinsiList = []; // Ensure list is empty on error
      } finally {
        this.isLoadingProvinsi = false;
      }
    },

    // Improve province selection to prevent UI jumps
    selectProvinsi(item) {
      // Prevent re-selecting the same province to avoid triggering unnecessary updates
      if (this.selectedProvinsi && this.selectedProvinsi.code === item.code) {
        this.showSuggestions = false;
        this.provinsiSelected = true;
        return;
      }

      console.log("Selected province:", item.name);

      // Set the lock during province selection
      this.isUpdating = true;

      // Update province selection
      this.selectedProvinsi = item;
      this.inputValue = item.name;
      this.showSuggestions = false;
      this.provinsiSelected = true; // Mark as selected

      // Clear previous kabupaten selections but preserve jalan and nomor
      const jalanToKeep = this.jalan;
      const nomorToKeep = this.nomor;

      this.selectedKabupaten = null;
      this.kabupatenInput = "";
      this.selectedKecamatan = null;
      this.kecamatanInput = "";
      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kabupatenSelected = false;
      this.kecamatanSelected = false;
      this.kelurahanSelected = false;

      // Restore jalan and nomor
      this.jalan = jalanToKeep;
      this.nomor = nomorToKeep;

      // Fetch kabupaten data and ensure the address is updated
      this.fetchKabupaten().then(() => {
        // Force an address update after province selection
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Debounced input handlers for better performance
    debouncedKabupatenInput(inputValue) {
      clearTimeout(this.inputDebounceTimers.kabupaten);
      this.inputDebounceTimers.kabupaten = setTimeout(() => {
        this.processKabupatenInput(inputValue);
      }, 150); // 150ms debounce
    },

    debouncedKecamatanInput(inputValue) {
      clearTimeout(this.inputDebounceTimers.kecamatan);
      this.inputDebounceTimers.kecamatan = setTimeout(() => {
        this.processKecamatanInput(inputValue);
      }, 150);
    },

    debouncedKelurahanInput(inputValue) {
      clearTimeout(this.inputDebounceTimers.kelurahan);
      this.inputDebounceTimers.kelurahan = setTimeout(() => {
        this.processKelurahanInput(inputValue);
      }, 150);
    },

    // Processing methods for debounced inputs
    processKabupatenInput(inputValue) {
      if (this.isUpdating) return;

      if (
        this.selectedKabupaten &&
        inputValue !== this.selectedKabupaten.name
      ) {
        this.kabupatenSelected = false;
        this.selectedKabupaten = null;
        this.selectedKecamatan = null;
        this.kecamatanInput = "";
        this.selectedKelurahan = null;
        this.kelurahanInput = "";
        this.kecamatanSelected = false;
        this.kelurahanSelected = false;
        this.kecamatanList = [];
        this.kelurahanList = [];
      }

      this.showKabupatenSuggestions = inputValue.length > 0;
    },

    processKecamatanInput(inputValue) {
      if (
        this.selectedKecamatan &&
        inputValue !== this.selectedKecamatan.name
      ) {
        this.kecamatanSelected = false;
        this.selectedKecamatan = null;
        this.selectedKelurahan = null;
        this.kelurahanInput = "";
        this.kelurahanSelected = false;
        this.kelurahanList = [];
      }

      this.showKecamatanSuggestions = inputValue.length > 0;
    },

    processKelurahanInput(inputValue) {
      if (
        this.selectedKelurahan &&
        inputValue !== this.selectedKelurahan.name
      ) {
        this.kelurahanSelected = false;
        this.selectedKelurahan = null;
      }

      this.showKelurahanSuggestions = inputValue.length > 0;
    },

    // Kabupaten handlers
    handleKabupatenInput(event) {
      if (event) event.stopPropagation();

      // Don't do anything if we're in updating mode
      if (this.isUpdating) return;

      // Use debounced input processing for better performance
      this.debouncedKabupatenInput(this.kabupatenInput);

      if (!this.isComposing) {
        this.$emit("kabupaten-change");
      }
    },

    handleKabupatenFocus() {
      // Only show suggestions if we haven't selected anything
      if (!this.kabupatenSelected && this.kabupatenInput) {
        this.showKabupatenSuggestions = true;
      }
    },

    handleKabupatenBlur() {
      setTimeout(() => {
        this.showKabupatenSuggestions = false;
        // Reset input if nothing selected and leaving the field
        if (!this.selectedKabupaten && !this.kabupatenSelected) {
          this.kabupatenInput = "";
        }
      }, 150);
    },

    handleKabupatenKeyup(event) {
      const inputEl = this.$refs.kabupatenInputEl;
      if (inputEl && this.kabupatenInput !== inputEl.value) {
        this.kabupatenInput = inputEl.value;
        this.handleKabupatenInput();
      }

      if (event.key === "Enter" && this.filteredKabupatenList.length > 0) {
        event.preventDefault();
        this.selectKabupaten(this.filteredKabupatenList[0]);
      }
    },

    handleKabupatenCompositionEnd(event) {
      this.isComposing = false;
      this.kabupatenInput = event.target.value;
      this.handleKabupatenInput();
    },

    selectKabupaten(item) {
      // Prevent re-selecting the same kabupaten
      if (this.selectedKabupaten && this.selectedKabupaten.code === item.code) {
        this.showKabupatenSuggestions = false;
        this.kabupatenSelected = true;
        return;
      }

      console.log("Selecting kabupaten:", item.name);

      // Set updating lock to prevent cycles
      this.isUpdating = true;

      // Preserve jalan and nomor
      const jalanToKeep = this.jalan;
      const nomorToKeep = this.nomor;

      // Update kabupaten selection
      this.selectedKabupaten = item;
      this.kabupatenInput = item.name;
      this.showKabupatenSuggestions = false;
      this.kabupatenSelected = true; // Mark as selected

      // Reset kecamatan and kelurahan
      this.selectedKecamatan = null;
      this.kecamatanInput = "";
      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kecamatanSelected = false;
      this.kelurahanSelected = false;

      // Clear existing kecamatan and kelurahan lists
      this.kecamatanList = [];
      this.kelurahanList = [];

      // Restore jalan and nomor after reset
      this.jalan = jalanToKeep;
      this.nomor = nomorToKeep;

      // Use a timeout to fetch kecamatan to ensure UI updates first
      this.fetchKecamatan().then(() => {
        // Release the updating lock and force address update
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Kecamatan handlers
    handleKecamatanInput(event) {
      event.stopPropagation(); // Stop event propagation

      // Use debounced input processing for better performance
      this.debouncedKecamatanInput(this.kecamatanInput);

      if (!this.isComposing) {
        this.$emit("kecamatan-change");
      }
    },

    handleKecamatanFocus() {
      // Only show suggestions if we haven't selected anything
      if (!this.kecamatanSelected && this.kecamatanInput) {
        this.showKecamatanSuggestions = true;
      }
    },

    handleKecamatanBlur() {
      setTimeout(() => {
        this.showKecamatanSuggestions = false;
        // Reset input if nothing selected and leaving the field
        if (!this.selectedKecamatan && !this.kecamatanSelected) {
          this.kecamatanInput = "";
        }
      }, 150);
    },

    handleKecamatanKeyup(event) {
      const inputEl = this.$refs.kecamatanInputEl;
      if (inputEl && this.kecamatanInput !== inputEl.value) {
        this.kecamatanInput = inputEl.value;
        this.handleKecamatanInput();
      }

      if (event.key === "Enter" && this.filteredKecamatanList.length > 0) {
        event.preventDefault();
        this.selectKecamatan(this.filteredKecamatanList[0]);
      }
    },

    handleKecamatanCompositionEnd(event) {
      this.isComposing = false;
      this.kecamatanInput = event.target.value;
      this.handleKecamatanInput();
    },

    selectKecamatan(item) {
      // Prevent re-selecting the same kecamatan
      if (this.selectedKecamatan && this.selectedKecamatan.code === item.code) {
        this.showKecamatanSuggestions = false;
        this.kecamatanSelected = true;
        return;
      }

      console.log("Selecting kecamatan:", item.name);

      // Set updating lock to prevent cycles
      this.isUpdating = true;

      this.selectedKecamatan = item;
      this.kecamatanInput = item.name;
      this.showKecamatanSuggestions = false;
      this.kecamatanSelected = true; // Mark as selected

      // Reset kelurahan
      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kelurahanSelected = false;
      this.kelurahanList = [];

      // Use a timeout to fetch kelurahan to ensure UI updates first
      this.fetchKelurahan().then(() => {
        // Release the updating lock and force address update
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Kelurahan handlers
    handleKelurahanInput(event) {
      event.stopPropagation(); // Stop event propagation

      // Use debounced input processing for better performance
      this.debouncedKelurahanInput(this.kelurahanInput);

      if (!this.isComposing) {
        this.$emit("kelurahan-change");
      }
    },

    handleKelurahanFocus() {
      // Only show suggestions if we haven't selected anything
      if (!this.kelurahanSelected && this.kelurahanInput) {
        this.showKelurahanSuggestions = true;
      }
    },

    handleKelurahanBlur() {
      setTimeout(() => {
        this.showKelurahanSuggestions = false;
        // Reset input if nothing selected and leaving the field
        if (!this.selectedKelurahan && !this.kelurahanSelected) {
          this.kelurahanInput = "";
        }
      }, 150);
    },

    handleKelurahanKeyup(event) {
      const inputEl = this.$refs.kelurahanInputEl;
      if (inputEl && this.kelurahanInput !== inputEl.value) {
        this.kelurahanInput = inputEl.value;
        this.handleKelurahanInput();
      }

      if (event.key === "Enter" && this.filteredKelurahanList.length > 0) {
        event.preventDefault();
        this.selectKelurahan(this.filteredKelurahanList[0]);
      }
    },

    handleKelurahanCompositionEnd(event) {
      this.isComposing = false;
      this.kelurahanInput = event.target.value;
      this.handleKelurahanInput();
    },

    selectKelurahan(item) {
      // Prevent re-selecting the same kelurahan
      if (this.selectedKelurahan && this.selectedKelurahan.code === item.code) {
        this.showKelurahanSuggestions = false;
        this.kelurahanSelected = true;
        return;
      }

      console.log("Selecting kelurahan:", item.name);

      // Set updating lock to prevent cycles
      this.isUpdating = true;

      // Trim any whitespace from kelurahan name to prevent issues
      this.selectedKelurahan = {
        ...item,
        name: item.name.trim(),
      };
      this.kelurahanInput = item.name.trim();
      this.showKelurahanSuggestions = false;
      this.kelurahanSelected = true; // Mark as selected

      // Force address update after selecting kelurahan
      setTimeout(() => {
        this.isUpdating = false;
        this.updateAlamat();
      }, 300);
    },

    // Data fetch methods
    async fetchKabupaten(retryCount = 0) {
      if (!this.selectedProvinsi) {
        console.log("Cannot fetch kabupaten: No province selected");
        return;
      }

      // Check cache first
      const cachedData = this.getCachedData(
        "regencies",
        this.selectedProvinsi.code,
      );
      if (cachedData) {
        this.kabupatenList = cachedData;
        this.$nextTick(() => {
          if (this.$refs.kabupatenInputEl && !this.kabupatenSelected) {
            this.$refs.kabupatenInputEl.focus();
          }
        });
        return cachedData;
      }

      console.log(
        "Fetching kabupaten for province:",
        this.selectedProvinsi.name,
        "code:",
        this.selectedProvinsi.code,
        retryCount > 0 ? `(retry ${retryCount})` : "",
      );

      try {
        this.isLoadingKabupaten = true;
        this.kabupatenList = []; // Clear existing list while loading

        // Add timeout and retry logic for better reliability
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(
          `/api/wilayah/regencies/${this.selectedProvinsi.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          // Handle specific HTTP status codes
          if (response.status === 502 && retryCount < 3) {
            console.warn(
              `502 error fetching kabupaten, retrying... (attempt ${retryCount + 1})`,
            );
            // Wait a bit before retrying
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKabupaten(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kabupaten: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();

        if (!data.data || !Array.isArray(data.data)) {
          console.error("Invalid kabupaten data format:", data);
          throw new Error("Invalid data format received");
        }

        // Set data and cache it
        this.kabupatenList = data.data;
        this.setCachedData("regencies", data.data, this.selectedProvinsi.code);

        console.log(
          `Loaded ${this.kabupatenList.length} kabupaten for ${this.selectedProvinsi.name}`,
        );

        return data.data;
      } catch (error) {
        console.error("Error fetching kabupaten:", error);

        // If it's a network error and we haven't retried too many times, try again
        if (
          (error.name === "AbortError" || error.message.includes("fetch")) &&
          retryCount < 2
        ) {
          console.warn(
            `Network error fetching kabupaten, retrying... (attempt ${retryCount + 1})`,
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKabupaten(retryCount + 1);
        }

        this.kabupatenList = []; // Ensure list is empty on error

        // Show user-friendly error message
        if (error.message.includes("502")) {
          console.warn(
            "Server temporarily unavailable. Please try selecting the province again.",
          );
        }

        return [];
      } finally {
        this.isLoadingKabupaten = false;

        // Always auto-focus the kabupaten input field after data is loaded
        this.$nextTick(() => {
          if (this.$refs.kabupatenInputEl && !this.kabupatenSelected) {
            this.$refs.kabupatenInputEl.focus();
          }
        });
      }
    },

    async fetchKecamatan(retryCount = 0) {
      if (!this.selectedKabupaten) {
        console.log("Cannot fetch kecamatan: No kabupaten selected");
        return [];
      }

      // Check cache first
      const cachedData = this.getCachedData(
        "districts",
        this.selectedKabupaten.code,
      );
      if (cachedData) {
        this.kecamatanList = cachedData;
        this.$nextTick(() => {
          if (this.$refs.kecamatanInputEl && !this.kecamatanSelected) {
            this.$refs.kecamatanInputEl.focus();
          }
        });
        return cachedData;
      }

      console.log(
        "Fetching kecamatan for kabupaten:",
        this.selectedKabupaten.name,
        "code:",
        this.selectedKabupaten.code,
        retryCount > 0 ? `(retry ${retryCount})` : "",
      );

      try {
        this.isLoadingKecamatan = true;
        this.kecamatanList = []; // Clear existing list while loading

        // Add timeout and retry logic for better reliability
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(
          `/api/wilayah/districts/${this.selectedKabupaten.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          // Handle specific HTTP status codes
          if (response.status === 502 && retryCount < 3) {
            console.warn(
              `502 error fetching kecamatan, retrying... (attempt ${retryCount + 1})`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKecamatan(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kecamatan: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();

        if (!data.data || !Array.isArray(data.data)) {
          console.error("Invalid kecamatan data format:", data);
          throw new Error("Invalid data format received");
        }

        // Set data and cache it
        this.kecamatanList = data.data;
        this.setCachedData("districts", data.data, this.selectedKabupaten.code);

        console.log(
          `Loaded ${this.kecamatanList.length} kecamatan for ${this.selectedKabupaten.name}`,
        );

        return data.data;
      } catch (error) {
        console.error("Error fetching kecamatan:", error);

        // If it's a network error and we haven't retried too many times, try again
        if (
          (error.name === "AbortError" || error.message.includes("fetch")) &&
          retryCount < 2
        ) {
          console.warn(
            `Network error fetching kecamatan, retrying... (attempt ${retryCount + 1})`,
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKecamatan(retryCount + 1);
        }

        this.kecamatanList = []; // Ensure list is empty on error
        return [];
      } finally {
        this.isLoadingKecamatan = false;

        // Always auto-focus the kecamatan input field after data is loaded
        this.$nextTick(() => {
          if (this.$refs.kecamatanInputEl && !this.kecamatanSelected) {
            this.$refs.kecamatanInputEl.focus();
          }
        });
      }
    },

    async fetchKelurahan(retryCount = 0) {
      if (!this.selectedKecamatan) {
        console.log("Cannot fetch kelurahan: No kecamatan selected");
        return;
      }

      // Check cache first
      const cachedData = this.getCachedData(
        "villages",
        this.selectedKecamatan.code,
      );
      if (cachedData) {
        this.kelurahanList = cachedData;
        this.$nextTick(() => {
          if (this.$refs.kelurahanInputEl) {
            this.$refs.kelurahanInputEl.focus();
          }
        });
        return cachedData;
      }

      console.log(
        "Fetching kelurahan for kecamatan:",
        this.selectedKecamatan.name,
        "code:",
        this.selectedKecamatan.code,
        retryCount > 0 ? `(retry ${retryCount})` : "",
      );

      try {
        this.isLoadingKelurahan = true;
        this.kelurahanList = []; // Clear existing list while loading

        // Add timeout and retry logic for better reliability
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(
          `/api/wilayah/villages/${this.selectedKecamatan.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          // Handle specific HTTP status codes
          if (response.status === 502 && retryCount < 3) {
            console.warn(
              `502 error fetching kelurahan, retrying... (attempt ${retryCount + 1})`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKelurahan(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kelurahan: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();

        if (!data.data || !Array.isArray(data.data)) {
          console.error("Invalid kelurahan data format:", data);
          throw new Error("Invalid data format received");
        }

        // Set data and cache it
        this.kelurahanList = data.data;
        this.setCachedData("villages", data.data, this.selectedKecamatan.code);

        console.log(
          `Loaded ${this.kelurahanList.length} kelurahan for ${this.selectedKecamatan.name}`,
        );

        // Auto-focus the kelurahan input field after data is loaded
        this.$nextTick(() => {
          if (this.$refs.kelurahanInputEl) {
            this.$refs.kelurahanInputEl.focus();
          }
        });

        return data.data;
      } catch (error) {
        console.error("Error fetching kelurahan:", error);

        // If it's a network error and we haven't retried too many times, try again
        if (
          (error.name === "AbortError" || error.message.includes("fetch")) &&
          retryCount < 2
        ) {
          console.warn(
            `Network error fetching kelurahan, retrying... (attempt ${retryCount + 1})`,
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKelurahan(retryCount + 1);
        }

        this.kelurahanList = []; // Ensure list is empty on error
        return [];
      } finally {
        this.isLoadingKelurahan = false;
      }
    },

    // Update address method to be more robust and prevent loops
    updateAlamat() {
      // Prevent recursive updates
      if (this.isUpdating) {
        return;
      }

      // Don't update while user is actively editing a field
      if (this.isUserEditing) {
        return;
      }

      // Set the lock
      this.isUpdating = true;

      // Clear any existing timer
      clearTimeout(this.updateTimer);

      // Get the most accurate values to use for street and number
      // Check for priority flag in localStorage
      let nomorPriority = false;
      try {
        nomorPriority =
          localStorage.getItem("alamat_nomor_priority") === "true";
      } catch (e) {
        console.warn("Could not read nomor priority", e);
      }

      // Use the best source for the nomor value based on priority
      let finalNomor = "";
      if (nomorPriority) {
        try {
          // First check localStorage
          const storedNomor = localStorage.getItem("alamat_nomor");
          if (storedNomor) {
            finalNomor = storedNomor;
            console.log("Using priority stored nomor:", finalNomor);
          }
        } catch (e) {
          console.warn("Could not read stored nomor", e);
        }
      }

      // If no priority value, use component state
      if (!finalNomor) {
        finalNomor = this.persistedNomor || this.nomor || "";
      }

      const currentJalan = this.persistedJalan || this.jalan || "";

      // Debounce updates to prevent rapid-fire changes
      this.updateTimer = setTimeout(() => {
        const alamatParts = [];

        // Always include jalan and nomor if they exist, using stored values
        if (currentJalan.trim() !== "") {
          alamatParts.push(`Jl. ${currentJalan.trim()},`);
        }

        if (finalNomor.trim() !== "") {
          alamatParts.push(`No. ${finalNomor.trim()},`);
        }

        // Only include kelurahan if it's actually selected
        if (this.selectedKelurahan && this.kelurahanSelected) {
          const kelurahanName = this.selectedKelurahan.name.trim();
          alamatParts.push(`Desa/Kel. ${kelurahanName},`);
        }

        // Only include kecamatan if it's actually selected
        if (this.selectedKecamatan && this.kecamatanSelected) {
          alamatParts.push(`Kec. ${this.selectedKecamatan.name},`);
        }

        if (this.selectedKabupaten?.name) {
          alamatParts.push(`${this.selectedKabupaten.name},`);
        }

        if (this.selectedProvinsi?.name) {
          alamatParts.push(`${this.selectedProvinsi.name}`);
        }

        // Only emit if we have parts to emit
        if (alamatParts.length > 0) {
          const alamat = alamatParts.join(" ");

          // Validate that our number is in the address
          if (finalNomor && !alamat.includes(`No. ${finalNomor}`)) {
            console.warn(
              "Number missing from address, forcing rebuild with:",
              finalNomor,
            );
            // Call the forced method instead
            this.forceAddressUpdateWithNumber(finalNomor);
            return;
          }

          console.log(
            "Emitting updated alamat with jalan:",
            currentJalan,
            "and nomor:",
            finalNomor,
          );

          // Save current values to component's state storage
          this.originalAddressState = {
            ...this.originalAddressState,
            jalan: currentJalan,
            nomor: finalNomor,
            provinsi: this.selectedProvinsi
              ? JSON.stringify(this.selectedProvinsi)
              : null,
            kabupaten: this.selectedKabupaten
              ? JSON.stringify(this.selectedKabupaten)
              : null,
            kecamatan: this.selectedKecamatan
              ? JSON.stringify(this.selectedKecamatan)
              : null,
            kelurahan: this.selectedKelurahan
              ? JSON.stringify(this.selectedKelurahan)
              : null,
          };

          this.$emit("input", alamat);

          // Store emitted address for comparison
          this.lastEmittedAddress = alamat;
        }

        // Release the lock after emission
        this.isUpdating = false;
      }, 300); // 300ms debounce
    },

    // Modify parseExistingAddress to better preserve user input
    parseExistingAddress(addressString) {
      // Only process if we have a valid string and we're not already updating
      if (
        this.isUpdating ||
        this.isUserEditing ||
        !addressString ||
        typeof addressString !== "string"
      ) {
        console.log(
          "Skipping address parsing:",
          this.isUpdating
            ? "update in progress"
            : this.isUserEditing
              ? "user is currently editing"
              : "invalid address string",
        );
        return;
      }

      console.log("Parsing address:", addressString);

      // Store current values before parsing - we'll re-apply them if they were set
      const currentJalan = this.jalan;
      const currentNomor = this.nomor;
      const hasUserSetJalan = currentJalan && currentJalan.trim() !== "";
      const hasUserSetNomor = currentNomor && currentNomor.trim() !== "";

      // Set update lock
      this.isUpdating = true;

      try {
        const parts = addressString.split(",").map((part) => part.trim());

        // First, try to get persisted values
        let persistedJalan = "";
        let persistedNomor = "";

        try {
          persistedJalan = localStorage.getItem("alamat_jalan") || "";
          persistedNomor = localStorage.getItem("alamat_nomor") || "";
        } catch (e) {
          console.warn("Could not read persisted address", e);
        }

        // If we have persisted values, use those preferentially
        if (persistedJalan) {
          this.jalan = persistedJalan;
          this.persistedJalan = persistedJalan;
          console.log("Using persisted jalan value:", persistedJalan);
        } else if (!hasUserSetJalan) {
          // Only extract jalan if user hasn't set it yet
          if (parts[0]?.toLowerCase().startsWith("jl.")) {
            this.jalan = parts[0].substring(3).trim();
            console.log("Found jalan in address:", this.jalan);
          }
        } else {
          console.log("Preserving user-set jalan:", currentJalan);
        }

        if (persistedNomor) {
          this.nomor = persistedNomor;
          this.persistedNomor = persistedNomor;
          console.log("Using persisted nomor value:", persistedNomor);
        } else if (!hasUserSetNomor) {
          // Only extract nomor if user hasn't set it yet
          const noPart = parts.find((p) => p?.toLowerCase().includes("no."));
          if (noPart) {
            this.nomor = noPart
              .substring(noPart.toLowerCase().indexOf("no.") + 3)
              .trim();
            console.log("Found nomor in address:", this.nomor);
          }
        } else {
          console.log("Preserving user-set nomor:", currentNomor);
        }

        // Extract kecamatan pattern - improved regex
        const kecamatanPattern = /kec\.|kecamatan/i;
        const kecPart = parts.find((p) => p && kecamatanPattern.test(p));
        let kecamatanName = "";
        if (kecPart) {
          kecamatanName = kecPart.replace(kecamatanPattern, "").trim();
          console.log("Found kecamatan in address:", kecamatanName);
        }

        // Extract kelurahan pattern - improved regex that covers all cases
        const kelurahanPattern = /desa\/kel\.|desa|kel\.|kelurahan/i;
        const kelPart = parts.find((p) => p && kelurahanPattern.test(p));
        let kelurahanName = "";
        if (kelPart) {
          kelurahanName = kelPart.replace(kelurahanPattern, "").trim();
          console.log("Found kelurahan in address:", kelurahanName);
        }

        // Extract kabupaten pattern - do this first since it's needed for parsing
        const kabupatenPattern = /kab\.|kabupaten|kota/i;
        const kabPart = parts.find((p) => p && kabupatenPattern.test(p));
        let kabupatenName = "";
        if (kabPart) {
          kabupatenName = kabPart.replace(kabupatenPattern, "").trim();
          console.log("Found kabupaten in address:", kabupatenName);
        }

        // Use a promise chain to make sure all data is loaded in the correct order
        this.fetchProvinsi()
          .then(async () => {
            // Find the province in the address (last part is usually province)
            const province = parts[parts.length - 1]?.trim() || "";

            if (province && this.provinsiList.length > 0) {
              const matchingProvince = this.provinsiList.find(
                (p) => p.name.toLowerCase() === province.toLowerCase(),
              );

              if (matchingProvince) {
                console.log("Found matching province:", matchingProvince.name);

                // Set selected province without triggering a fetch yet
                this.selectedProvinsi = matchingProvince;
                this.inputValue = matchingProvince.name;
                this.provinsiSelected = true; // Mark as selected

                // Only proceed with kabupaten if we have one
                if (kabupatenName) {
                  return this.fetchKabupaten().then(async () => {
                    if (this.kabupatenList.length > 0) {
                      console.log("Looking for kabupaten:", kabupatenName);

                      // Find matching kabupaten with flexible matching
                      const matchingKabupaten = this.kabupatenList.find(
                        (k) =>
                          kabupatenName
                            .toLowerCase()
                            .includes(k.name.toLowerCase()) ||
                          k.name
                            .toLowerCase()
                            .includes(kabupatenName.toLowerCase()),
                      );

                      if (matchingKabupaten) {
                        console.log(
                          "Found matching kabupaten:",
                          matchingKabupaten.name,
                        );
                        this.selectedKabupaten = matchingKabupaten;
                        this.kabupatenInput = matchingKabupaten.name;
                        this.kabupatenSelected = true; // Mark as selected

                        // If we have kecamatan info, try to find it
                        if (kecamatanName) {
                          return this.fetchKecamatan().then(async () => {
                            if (this.kecamatanList.length > 0) {
                              console.log(
                                "Looking for kecamatan:",
                                kecamatanName,
                              );

                              // Find matching kecamatan with flexible matching
                              const matchingKecamatan = this.kecamatanList.find(
                                (k) =>
                                  kecamatanName
                                    .toLowerCase()
                                    .includes(k.name.toLowerCase()) ||
                                  k.name
                                    .toLowerCase()
                                    .includes(kecamatanName.toLowerCase()),
                              );

                              if (matchingKecamatan) {
                                console.log(
                                  "Found matching kecamatan:",
                                  matchingKecamatan.name,
                                );
                                this.selectedKecamatan = matchingKecamatan;
                                this.kecamatanInput = matchingKecamatan.name;
                                this.kecamatanSelected = true; // Mark as selected

                                // Final check for kelurahan
                                if (kelurahanName) {
                                  return this.fetchKelurahan().then(() => {
                                    if (this.kelurahanList.length > 0) {
                                      console.log(
                                        "Looking for kelurahan:",
                                        kelurahanName,
                                      );

                                      // More exact matching for kelurahan - trim spaces and use exact matching first
                                      let matchingKelurahan =
                                        this.kelurahanList.find(
                                          (k) =>
                                            k.name.trim().toLowerCase() ===
                                            kelurahanName.toLowerCase(),
                                        );

                                      // If no exact match, try substring matching
                                      if (!matchingKelurahan) {
                                        matchingKelurahan =
                                          this.kelurahanList.find(
                                            (k) =>
                                              kelurahanName
                                                .toLowerCase()
                                                .includes(
                                                  k.name.trim().toLowerCase(),
                                                ) ||
                                              k.name
                                                .trim()
                                                .toLowerCase()
                                                .includes(
                                                  kelurahanName.toLowerCase(),
                                                ),
                                          );
                                      }

                                      if (matchingKelurahan) {
                                        console.log(
                                          "Found matching kelurahan:",
                                          matchingKelurahan.name,
                                        );

                                        // Ensure the name is trimmed to avoid trailing spaces
                                        this.selectedKelurahan = {
                                          ...matchingKelurahan,
                                          name: matchingKelurahan.name.trim(),
                                        };
                                        this.kelurahanInput =
                                          matchingKelurahan.name.trim();
                                        this.kelurahanSelected = true; // Mark as selected
                                      }
                                    }
                                  });
                                }
                              }
                            }
                          });
                        }
                      }
                    }
                  });
                }
              }
            }
          })
          .catch((error) => {
            console.error("Error in address parsing chain:", error);
          })
          .finally(() => {
            // After all parsing completes, save the current state to enable comparison
            this.originalAddressState = {
              jalan: this.jalan,
              nomor: this.nomor,
              provinsi: this.selectedProvinsi
                ? JSON.stringify(this.selectedProvinsi)
                : null,
              kabupaten: this.selectedKabupaten
                ? JSON.stringify(this.selectedKabupaten)
                : null,
              kecamatan: this.selectedKecamatan
                ? JSON.stringify(this.selectedKecamatan)
                : null,
              kelurahan: this.selectedKelurahan
                ? JSON.stringify(this.selectedKelurahan)
                : null,
            };

            // Release the update lock after a delay to allow all processing to complete
            setTimeout(() => {
              this.isUpdating = false;
            }, 1000);
          });
      } catch (error) {
        console.error("Error parsing address:", error);
        this.isUpdating = false;
      }
    },

    forceAddressUpdate() {
      console.log("Force updating address before component destroyed");

      // Try to get the most accurate number
      let mostAccurateNomor = "";

      // Check for priority number first
      try {
        if (localStorage.getItem("alamat_nomor_priority") === "true") {
          mostAccurateNomor = localStorage.getItem("alamat_nomor") || "";
        }
      } catch (e) {
        console.warn("Could not read priority nomor", e);
      }

      // If no priority number, try component state
      if (!mostAccurateNomor) {
        mostAccurateNomor = this.persistedNomor || this.nomor || "";
      }

      console.log(
        "Force updating with most accurate nomor:",
        mostAccurateNomor,
      );

      // Use the specialized method for number updates
      if (mostAccurateNomor) {
        this.forceAddressUpdateWithNumber(mostAccurateNomor);
      } else {
        // Fall back to regular update if no number
        // Removed unused currentJalan variable
        this.updateAlamat();
      }
    },

    // Add method to clear all cached address data
    clearStoredAddressData() {
      console.log("Clearing all stored address data from caches");
      try {
        // Clear all address-related items from localStorage
        localStorage.removeItem("alamat_jalan");
        localStorage.removeItem("alamat_jalan_temp");
        localStorage.removeItem("alamat_nomor");
        localStorage.removeItem("alamat_nomor_temp");
        localStorage.removeItem("alamat_nomor_priority");

        // Clear from sessionStorage too
        sessionStorage.removeItem("alamat_nomor_current");

        // Any other address-related storage items should be cleared here
      } catch (e) {
        console.warn("Error clearing address storage:", e);
      }
    },
  },
  created() {
    console.log("AlamatTinggalForm created with value:", this.value);

    // Clear stored address data on page reload (not on hot-module-replacement)
    // Check if this is a genuine page load/reload by examining navigation type
    const isPageReload =
      window.performance &&
      (window.performance.getEntriesByType("navigation")[0]?.type ===
        "navigate" || // Navigate
        window.performance.getEntriesByType("navigation")[0]?.type ===
          "reload"); // Reload

    if (isPageReload || document.readyState === "complete") {
      console.log("Page was loaded/reloaded - clearing address cache");
      this.clearStoredAddressData();
    }

    if (this.value === undefined || this.value === null) {
      this.$emit("input", "");
    }
  },
  mounted() {
    this.fetchProvinsi();
    console.log("AlamatTinggalForm mounted");

    // Start prefetching popular regions in the background for better performance
    setTimeout(() => {
      this.prefetchPopularRegions();
    }, 1000); // Wait 1 second to not interfere with initial load

    // Alternative reload detection technique as backup
    // Using a timestamp in sessionStorage to detect genuine page reloads
    try {
      const lastLoad = sessionStorage.getItem("alamat_last_load_time");
      const currentTime = Date.now();

      // If no timestamp or it's been more than 2 seconds since last load,
      // consider this a page reload rather than a hot-component remount
      if (!lastLoad || currentTime - Number.parseInt(lastLoad, 10) > 2000) {
        console.log(
          "Detected page load via timestamp - clearing address cache",
        );
        this.clearStoredAddressData();
      }

      // Update the timestamp for next detection
      sessionStorage.setItem("alamat_last_load_time", currentTime.toString());
    } catch (e) {
      console.warn("Error in reload detection:", e);
    }

    // Initialize originalAddressState to prevent undefined errors
    this.originalAddressState = {
      jalan: this.jalan || "",
      nomor: this.nomor || "",
      provinsi: this.selectedProvinsi
        ? JSON.stringify(this.selectedProvinsi)
        : null,
      kabupaten: this.selectedKabupaten
        ? JSON.stringify(this.selectedKabupaten)
        : null,
      kecamatan: this.selectedKecamatan
        ? JSON.stringify(this.selectedKecamatan)
        : null,
      kelurahan: this.selectedKelurahan
        ? JSON.stringify(this.selectedKelurahan)
        : null,
    };

    // Save initial state to prevent data loss in repeated edits
    this.$nextTick(() => {
      this.originalAddressState = {
        jalan: this.jalan,
        nomor: this.nomor,
        provinsi: this.selectedProvinsi
          ? JSON.stringify(this.selectedProvinsi)
          : null,
        kabupaten: this.selectedKabupaten
          ? JSON.stringify(this.selectedKabupaten)
          : null,
        kecamatan: this.selectedKecamatan
          ? JSON.stringify(this.selectedKecamatan)
          : null,
        kelurahan: this.selectedKelurahan
          ? JSON.stringify(this.selectedKelurahan)
          : null,
      };

      // Force an initial address update if we have a partial address
      if (
        this.value &&
        ((this.selectedProvinsi &&
          !this.value.includes(this.selectedProvinsi.name)) ||
          (this.selectedKabupaten &&
            !this.value.includes(this.selectedKabupaten.name)) ||
          (this.selectedKecamatan &&
            !this.value.includes(this.selectedKecamatan.name)) ||
          (this.selectedKelurahan &&
            !this.value.includes(this.selectedKelurahan.name)))
      ) {
        console.log("Found incomplete address, forcing update");
        this.forceAddressUpdate();
      }
    });

    // Try to restore persisted values on mount
    try {
      const savedJalan = localStorage.getItem("alamat_jalan");
      const savedNomor = localStorage.getItem("alamat_nomor");

      if (savedJalan) {
        this.jalan = savedJalan;
        this.persistedJalan = savedJalan;
        console.log("Restored persisted jalan:", savedJalan);
      }

      if (savedNomor) {
        this.nomor = savedNomor;
        this.persistedNomor = savedNomor;
        console.log("Restored persisted nomor:", savedNomor);
      }
    } catch (e) {
      console.warn("Could not restore persisted address", e);
    }
  },

  // Add a beforeDestroy hook to compare and restore state if needed
  beforeDestroy() {
    // Get the most accurate number
    let finalNomor = "";
    try {
      if (localStorage.getItem("alamat_nomor_priority") === "true") {
        finalNomor = localStorage.getItem("alamat_nomor") || "";
      }
    } catch (e) {
      console.warn("Could not read priority nomor on destroy", e);
    }

    if (!finalNomor) {
      finalNomor = this.persistedNomor || this.nomor || "";
    }

    // Force a final update with the correct number
    if (finalNomor) {
      this.forceAddressUpdateWithNumber(finalNomor);
    } else {
      this.forceAddressUpdate();
    }
  },

  // For Vue 3 compatibility (in case the application is using Vue 3)
  beforeUnmount() {
    // Clean up timers to prevent memory leaks
    this.cleanupTimers();

    // Similar to beforeDestroy, make sure the number is correct
    let finalNomor = "";
    try {
      if (localStorage.getItem("alamat_nomor_priority") === "true") {
        finalNomor = localStorage.getItem("alamat_nomor") || "";
      }
    } catch (e) {
      console.warn("Could not read priority nomor on unmount", e);
    }

    if (!finalNomor) {
      finalNomor = this.persistedNomor || this.nomor || "";
    }

    // Force a final update with the correct number
    if (finalNomor) {
      this.forceAddressUpdateWithNumber(finalNomor);
    } else {
      this.forceAddressUpdate();
    }
  },
};
</script>

<style scoped>
/* Reset styles for input and label elements */
:deep(.form-row label),
:deep(.form-row input),
:deep(.form-row .suggestions-container input),
:deep(.form-row select) {
  all: unset;
  box-sizing: border-box;
}

/* Form row styling */
:deep(.form-row) {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
}

/* Label styling */
:deep(.form-row label) {
  min-width: 90px;
  margin-right: 6px;
  text-align: left;
  font-weight: bold;
}

/* Input and select styling */
:deep(.form-row input),
:deep(.form-row .suggestions-container input),
:deep(.form-row select) {
  flex: 1;
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* Suggestions container styling */
:deep(.suggestions-container) {
  position: relative;
  flex: 1;
  width: 100%;
}

/* Suggestions dropdown styling */
:deep(.suggestions) {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

/* Suggestion item styling */
:deep(.suggestion-item) {
  padding: 8px;
  cursor: pointer;
}

:deep(.suggestion-item:hover) {
  background-color: #f0f0f0;
}

/* Loading indicator styling */
:deep(.loading-indicator) {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  padding: 8px;
  text-align: center;
}
</style>
