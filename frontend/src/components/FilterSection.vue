<template>
  <section>
    <div class="filter-item">
      <div>
        <label for="desaFilter">Filter Desa:</label>
        <select
          v-model="localFilters.sambung_desa"
          @change="emitFilterChange"
          id="desaFilter"
        >
          <option value="">Semua</option>
          <option
            v-for="desa in uniqueDesa"
            :key="desa"
            :value="desa"
          >
            {{ desa }}
          </option>
        </select>
      </div>
      <div>
        <label for="kelompokFilter">Filter Kelompok:</label>
        <select
          v-model="localFilters.sambung_kelompok"
          @change="emitFilterChange"
          id="kelompokFilter"
        >
          <option value="">Semua</option>
          <option
            v-for="kelompok in uniqueKelompok"
            :key="kelompok"
            :value="kelompok"
          >
            {{ kelompok }}
          </option>
        </select>
      </div>
      <div>
        <label for="namaFilter">Filter Nama:</label>
        <input
          type="text"
          v-model="localFilters.nama"
          @input="emitFilterChange"
          placeholder="Cari nama..."
          id="namaFilter"
        />
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "FilterSection",
  props: {
    filters: {
      type: Object,
      required: true,
    },
    uniqueDesa: {
      type: Array,
      required: true,
    },
    uniqueKelompok: {
      type: Array,
      required: true,
    },
  },
  emits: ["filter-change"],
  data() {
    return {
      localFilters: { ...this.filters },
    };
  },
  watch: {
    filters: {
      handler(newFilters) {
        this.localFilters = { ...newFilters };
      },
      deep: true,
    },
  },
  methods: {
    emitFilterChange() {
      this.$emit("filter-change", { ...this.localFilters });
    },
  },
};
</script>

<style scoped>
section {
  width: 100%;
  display: flex;
  justify-content: center;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #ffffff;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  margin-bottom: 20px;
  max-width: 550px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.filter-item div {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: 5px;
  font-weight: bold;
}

select,
input {
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  appearance: none;
  border-radius: 20px;
  box-sizing: border-box;
  width: 100%;
  height: 45px;
  margin-top: 0;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    max-width: 100%;
  }
}
</style>
