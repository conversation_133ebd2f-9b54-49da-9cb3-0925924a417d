<template>
  <div v-if="show" class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Detail Biodata</h3>
        <button class="close-button" @click="$emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <div v-if="data" class="detail-grid">
          <div class="detail-section">
            <h4>Informasi Pribadi</h4>
            <p><strong>Nama <PERSON>kap:</strong> {{ data.nama_lengkap }}</p>
            <p><strong>Nama Panggilan:</strong> {{ data.nama_panggilan }}</p>
            <p><strong>Tempat Lahir:</strong> {{ data.kelahiran_tempat }}</p>
            <p>
              <strong>Tanggal Lahir:</strong>
              {{ formatDate(data.kelahiran_tanggal) }}
            </p>
            <p><strong>Sekolah/Kelas:</strong> {{ data.sekolah_kelas }}</p>
            <p><strong>No. HP:</strong> {{ data.nomor_hape || "-" }}</p>
            <p><strong>Jenis Kelamin:</strong> {{ data.jenis_kelamin }}</p>
            <p><strong>Hobi:</strong> {{ formatHobi(data.hobi) }}</p>
            <p><strong>Alamat:</strong> {{ formattedAlamat }}</p>
            <p>
              <strong>Tanggal Pendataan:</strong>
              {{ formatDate(data.pendataan_tanggal) }}
            </p>
          </div>
          <div class="detail-section">
            <h4>Informasi Keluarga</h4>
            <p><strong>Nama Ayah:</strong> {{ data.nama_ayah }}</p>
            <p><strong>Status Ayah:</strong> {{ data.status_ayah }}</p>
            <p>
              <strong>No. HP Ayah:</strong> {{ data.nomor_hape_ayah || "-" }}
            </p>
            <p><strong>Nama Ibu:</strong> {{ data.nama_ibu }}</p>
            <p><strong>Status Ibu:</strong> {{ data.status_ibu }}</p>
            <p><strong>No. HP Ibu:</strong> {{ data.nomor_hape_ibu || "-" }}</p>
          </div>
          <div class="detail-section">
            <h4>Sambung</h4>
            <p><strong>Daerah:</strong> {{ data.daerah }}</p>
            <p><strong>Sambung Desa:</strong> {{ data.sambung_desa }}</p>
            <p>
              <strong>Sambung Kelompok:</strong> {{ data.sambung_kelompok }}
            </p>
          </div>
          <div class="detail-section photo-section" v-if="data.foto_filename">
            <h4>Foto</h4>
            <img
              v-if="photoUrl"
              :src="photoUrl"
              :alt="`Foto ${data.nama_lengkap}`"
              class="biodata-photo"
              @error="handleImageError"
            />
            <div v-else-if="loadingPhoto" class="loading-photo">
              <p>Memuat foto...</p>
            </div>
            <p v-else class="no-photo">Foto tidak tersedia</p>
          </div>
          <div class="detail-section" v-else>
            <h4>Foto</h4>
            <p class="no-photo">Foto tidak tersedia</p>
          </div>
        </div>
        <div v-else class="loading-content">
          <p>Memuat detail...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BiodataDetailModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
    apiKey: {
      type: String,
      required: true,
    },
  },
  emits: ["close"],
  computed: {
    formattedAlamat() {
      if (!this.data) return "";
      const {
        alamat_jalan,
        alamat_nomor,
        alamat_kelurahandesa,
        alamat_kecamatan,
        alamat_kabupatenkota,
        alamat_provinsi,
      } = this.data;

      const parts = [];
      if (alamat_jalan) parts.push(`Jl. ${alamat_jalan}`);
      if (alamat_nomor) parts.push(`No. ${alamat_nomor}`);
      if (alamat_kelurahandesa) parts.push(`Desa/Kel. ${alamat_kelurahandesa}`);
      if (alamat_kecamatan) parts.push(`Kec. ${alamat_kecamatan}`);
      if (alamat_kabupatenkota) parts.push(alamat_kabupatenkota);
      if (alamat_provinsi) parts.push(alamat_provinsi);

      return parts.join(", ");
    },
  },
  data() {
    return {
      photoUrl: null,
      loadingPhoto: false,
    };
  },
  watch: {
    data: {
      handler(newData) {
        if (newData && newData.foto_filename) {
          this.loadPhoto(newData.foto_filename);
        } else {
          this.photoUrl = null;
          this.loadingPhoto = false;
        }
      },
      immediate: true,
    },
    show(newShow) {
      if (!newShow) {
        // Clean up photo URL when modal is closed
        if (this.photoUrl) {
          URL.revokeObjectURL(this.photoUrl);
          this.photoUrl = null;
        }
      }
    },
  },
  methods: {
    async loadPhoto(filename) {
      this.loadingPhoto = true;
      this.photoUrl = null;

      const photoUrl = `/api/biodata/generus/foto/${filename}`;
      console.log("🔍 [PHOTO DEBUG] Starting photo load process");
      console.log("📁 [PHOTO DEBUG] Filename:", filename);
      console.log("🌐 [PHOTO DEBUG] Full URL:", photoUrl);
      console.log(
        "🔑 [PHOTO DEBUG] API Key (first 10 chars):",
        this.apiKey ? this.apiKey.substring(0, 10) + "..." : "NOT PROVIDED",
      );
      console.log("📋 [PHOTO DEBUG] Request headers:", {
        Authorization: `ApiKey ${this.apiKey}`,
      });

      try {
        console.log("🚀 [PHOTO DEBUG] Making fetch request...");
        const response = await fetch(photoUrl, {
          headers: {
            Authorization: `ApiKey ${this.apiKey}`,
          },
        });

        console.log("📡 [PHOTO DEBUG] Response received");
        console.log("📊 [PHOTO DEBUG] Response status:", response.status);
        console.log(
          "📊 [PHOTO DEBUG] Response status text:",
          response.statusText,
        );
        console.log(
          "📊 [PHOTO DEBUG] Response headers:",
          Object.fromEntries(response.headers.entries()),
        );
        console.log("📊 [PHOTO DEBUG] Response URL:", response.url);
        console.log("📊 [PHOTO DEBUG] Response type:", response.type);

        if (!response.ok) {
          console.error("❌ [PHOTO DEBUG] Response not OK");
          console.error("❌ [PHOTO DEBUG] Status:", response.status);
          console.error("❌ [PHOTO DEBUG] Status text:", response.statusText);

          // Try to get response body for more error details
          try {
            const errorText = await response.text();
            console.error("❌ [PHOTO DEBUG] Error response body:", errorText);
          } catch (bodyError) {
            console.error(
              "❌ [PHOTO DEBUG] Could not read error response body:",
              bodyError,
            );
          }

          throw new Error(
            `HTTP error! status: ${response.status} - ${response.statusText}`,
          );
        }

        console.log("✅ [PHOTO DEBUG] Response OK, converting to blob...");
        const blob = await response.blob();
        console.log("📦 [PHOTO DEBUG] Blob created, size:", blob.size, "bytes");
        console.log("📦 [PHOTO DEBUG] Blob type:", blob.type);

        this.photoUrl = URL.createObjectURL(blob);
        console.log(
          "🖼️ [PHOTO DEBUG] Photo URL created successfully:",
          this.photoUrl,
        );
      } catch (error) {
        console.error("💥 [PHOTO DEBUG] Error in loadPhoto method:");
        console.error("💥 [PHOTO DEBUG] Error type:", error.constructor.name);
        console.error("💥 [PHOTO DEBUG] Error message:", error.message);
        console.error("💥 [PHOTO DEBUG] Full error object:", error);
        console.error("💥 [PHOTO DEBUG] Stack trace:", error.stack);
        this.photoUrl = null;
      } finally {
        this.loadingPhoto = false;
        console.log("🏁 [PHOTO DEBUG] Photo loading process completed");
      }
    },
    formatDate(dateString) {
      if (!dateString) return "-";
      const date = new Date(dateString);
      return date.toLocaleDateString("id-ID", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
    formatHobi(hobi) {
      if (!hobi) return "-";
      if (typeof hobi === "object") {
        return Object.values(hobi).join(", ") || "-";
      }
      return hobi;
    },
    handleImageError(_event) {
      console.error("Image failed to load");
      this.photoUrl = null;
    },
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  border-radius: 15px;
  max-width: 95vw;
  max-height: 95vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 2px solid #2e5a35;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 2px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2e5a35;
  font-size: 20px;
}

.close-button {
  background: #ff4444;
  color: white;
  border: none;
  font-size: 18px;
  cursor: pointer;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.2s;
}

.close-button:hover {
  background: #ff6666;
  transform: scale(1.1);
}

.modal-body {
  padding: 25px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.detail-section {
  background-color: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #2e5a35;
  font-size: 16px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.detail-section strong {
  color: #495057;
}

.photo-section {
  text-align: center;
}

.biodata-photo {
  max-width: 200px;
  max-height: 250px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.no-photo {
  color: #6c757d;
  font-style: italic;
  margin-top: 10px;
}

.loading-content {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.loading-photo {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 20px;
  }

  .close-button {
    width: 30px;
    height: 30px;
    font-size: 16px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .biodata-photo {
    max-width: 150px;
    max-height: 200px;
  }

  .detail-section {
    padding: 12px;
  }
}
</style>
