<template>
    <div class="form-group">
        <label for="kelompok">Alamat Sambung</label>
        <div class="suggestions-container">
            <input
                id="kelompok"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                :placeholder="placeholder"
                required
            />
            <div v-if="isLoading" class="loading-indicator">Loading...</div>
            <div v-if="loadError" class="error-message">{{ loadError }}</div>
            <div
                class="suggestions"
                v-if="showSuggestions && filteredKelompok.length"
            >
                <div
                    v-for="(item, index) in filteredKelompok"
                    :key="index"
                    class="suggestion-item"
                    @click="selectKelompok(item)"
                >
                    {{ item.kelompok }} ({{ item.desa }})
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SuggestionsMixin from "../mixins/SuggestionsMixin";

export default {
    mixins: [SuggestionsMixin],
    props: {
        formData: {
            type: Object,
            required: true,
        },
        kelompokOptions: {
            type: Object,
            required: true,
        },
        flattenedKelompok: {
            type: Array,
            required: true,
        },
        dataLoaded: {
            type: Boolean,
            required: true,
        },
        isLoading: {
            type: Boolean,
            required: true,
        },
        loadError: {
            type: String,
            default: null,
        },
        placeholder: {
            type: String,
            default: "Ketik untuk mencari kelompok atau desa.",
        },
    },
    computed: {
        filteredKelompok() {
            const searchTerm = this.inputValue.toLowerCase();
            if (!searchTerm || !this.dataLoaded) return [];

            const seen = new Set();
            return this.flattenedKelompok.filter(({ kelompok, desa }) => {
                const identifier = `${kelompok.toLowerCase()}-${desa.toLowerCase()}`;
                const matches =
                    kelompok.toLowerCase().includes(searchTerm) ||
                    desa.toLowerCase().includes(searchTerm);
                if (matches && !seen.has(identifier)) {
                    seen.add(identifier);
                    return true;
                }
                return false;
            });
        },
    },
    methods: {
        handleInput() {
            this.showSuggestions = true;
            this.formData.sambung_desa = "";
            this.formData.sambung_kelompok = "";
            this.$emit("input-change"); // Notify parent component
        },
        selectKelompok({ kelompok, desa }) {
            this.inputValue = `${kelompok} (${desa})`;
            this.formData.sambung_kelompok = kelompok;
            this.formData.sambung_desa = desa;
            this.showSuggestions = false;
        },
    },
};
</script>
