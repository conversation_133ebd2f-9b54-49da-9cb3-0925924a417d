<template>
  <section class="statistics-section">
    <div class="stats-container">
      <div class="stat-card">
        <h3>Total</h3>
        <p class="stat-number">{{ stats.total_count }}</p>
      </div>
      <div class="stat-card">
        <h3><PERSON><PERSON>-laki</h3>
        <p class="stat-number">{{ stats.male_count }}</p>
      </div>
      <div class="stat-card">
        <h3>Perempuan</h3>
        <p class="stat-number">{{ stats.female_count }}</p>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "StatisticsSection",
  props: {
    stats: {
      type: Object,
      required: true,
      default: () => ({
        total_count: 0,
        male_count: 0,
        female_count: 0,
      }),
    },
  },
};
</script>

<style scoped>
.statistics-section {
  margin: 15px 0;
}

.stats-container {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 800px;
  margin: 0 auto;
}

.stat-card {
  background-color: #ffffff;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 100px;
  flex: 1;
}

.stat-card h3 {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.stat-number {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  color: #2e5a35;
}

@media (max-width: 768px) {
  .stats-container {
    flex-direction: column;
    gap: 8px;
  }

  .stat-card {
    min-width: auto;
    padding: 10px 14px;
  }

  .stat-number {
    font-size: 20px;
  }
}
</style>
