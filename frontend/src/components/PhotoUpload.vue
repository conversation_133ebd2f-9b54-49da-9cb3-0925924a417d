<template>
  <div class="photo-upload-container">
    <div class="section-title">Foto Profil</div>

    <!-- Photo Preview -->
    <div v-if="displayPreviewUrl" class="photo-preview">
      <img :src="displayPreviewUrl" alt="Preview foto" class="preview-image" />
      <button @click="removePhoto" class="remove-photo-btn" type="button">
        &times; Hapus Foto
      </button>
      <div v-if="existingPhotoUrl && !previewUrl" class="existing-photo-label">
        Foto yang sudah ada
      </div>
    </div>

    <!-- Loading existing photo -->
    <div v-if="loadingExistingPhoto" class="loading-photo">
      <p>Memuat foto yang sudah ada...</p>
    </div>

    <!-- Upload Options -->
    <div v-if="!displayPreviewUrl && !loadingExistingPhoto" class="upload-options">
      <!-- Camera Capture -->
      <div class="camera-section">
        <button
          @click="openCamera"
          :disabled="isCapturing || !cameraSupported"
          class="camera-btn"
          type="button"
        >
          📷 {{ isCapturing ? "Membuka Kamera..." : "Ambil Foto" }}
        </button>
        <p v-if="!cameraSupported" class="camera-warning">
          Kamera tidak tersedia di perangkat ini
        </p>
      </div>

      <!-- File Upload -->
      <div class="file-section">
        <label for="photo-file" class="file-upload-label">
          📁 Pilih File Foto (PNG, Max 2MB)
        </label>
        <input
          id="photo-file"
          type="file"
          accept="image/png"
          @change="handleFileSelect"
          class="file-input"
          ref="fileInput"
        />
      </div>
    </div>

    <!-- Camera Modal -->
    <div v-if="showCamera" class="camera-modal" @click="closeCamera">
      <div class="camera-content" @click.stop>
        <div class="camera-header">
          <h3>Ambil Foto</h3>
          <div class="camera-controls-header">
            <button @click="switchCamera" class="switch-camera-btn" type="button" v-if="hasMultipleCameras">
              🔄 Ganti Kamera
            </button>
            <button @click="closeCamera" class="close-btn" type="button">
              ✕
            </button>
          </div>
        </div>

        <div class="camera-viewport">
          <video
            ref="videoElement"
            autoplay
            playsinline
            class="camera-video"
          ></video>
          <canvas
            ref="canvasElement"
            class="camera-canvas"
            style="display: none"
          ></canvas>
        </div>

        <div class="camera-controls">
          <button @click="capturePhoto" class="capture-btn" type="button">
            📸 Ambil Foto
          </button>
          <button @click="closeCamera" class="cancel-btn" type="button">
            Batal
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
export default {
  name: "PhotoUpload",
  props: {
    modelValue: {
      type: File,
      default: null,
    },
    existingPhotoFilename: {
      type: String,
      default: null,
    },
    apiKey: {
      type: String,
      default: null,
    },
  },
  emits: ["update:modelValue"],
  data() {
    return {
      previewUrl: null,
      showCamera: false,
      isCapturing: false,
      cameraSupported: false,
      stream: null,
      errorMessage: null,
      currentFacingMode: "user", // "user" for front camera, "environment" for back camera
      hasMultipleCameras: false,
      loadingExistingPhoto: false,
      existingPhotoUrl: null,
    };
  },
  computed: {
    displayPreviewUrl() {
      // Show new photo preview if available, otherwise show existing photo
      return this.previewUrl || this.existingPhotoUrl;
    },
  },
  async mounted() {
    await this.checkCameraSupport();
    // Load existing photo if provided
    if (this.existingPhotoFilename && this.apiKey) {
      await this.loadExistingPhoto();
    }
  },
  beforeUnmount() {
    this.stopCamera();
    // Clean up existing photo URL
    if (this.existingPhotoUrl) {
      URL.revokeObjectURL(this.existingPhotoUrl);
    }
  },
  watch: {
    existingPhotoFilename: {
      handler(newFilename) {
        if (newFilename && this.apiKey) {
          this.loadExistingPhoto();
        } else {
          this.clearExistingPhoto();
        }
      },
      immediate: true,
    },
    apiKey: {
      handler(newApiKey) {
        if (newApiKey && this.existingPhotoFilename) {
          this.loadExistingPhoto();
        }
      },
    },
  },
  methods: {
    async checkCameraSupport() {
      this.cameraSupported = !!(
        navigator.mediaDevices && navigator.mediaDevices.getUserMedia
      );

      if (this.cameraSupported) {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          const videoDevices = devices.filter(device => device.kind === 'videoinput');
          this.hasMultipleCameras = videoDevices.length > 1;
        } catch (error) {
          console.error("Error checking camera devices:", error);
        }
      }
    },

    async openCamera() {
      if (!this.cameraSupported) {
        this.errorMessage = "Kamera tidak didukung di perangkat ini";
        return;
      }

      this.isCapturing = true;
      this.errorMessage = null;

      try {
        this.stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: this.currentFacingMode,
            width: { ideal: 640 },
            height: { ideal: 480 },
          },
        });

        this.showCamera = true;
        this.$nextTick(() => {
          if (this.$refs.videoElement) {
            this.$refs.videoElement.srcObject = this.stream;
          }
        });
      } catch (error) {
        console.error("Error accessing camera:", error);
        this.errorMessage =
          "Tidak dapat mengakses kamera. Pastikan izin kamera telah diberikan.";
      } finally {
        this.isCapturing = false;
      }
    },

    closeCamera() {
      this.stopCamera();
      this.showCamera = false;
    },

    stopCamera() {
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
      }
    },

    async switchCamera() {
      if (!this.hasMultipleCameras) return;

      // Stop current stream
      this.stopCamera();

      // Switch facing mode
      this.currentFacingMode = this.currentFacingMode === "user" ? "environment" : "user";

      // Restart camera with new facing mode
      try {
        this.stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: this.currentFacingMode,
            width: { ideal: 640 },
            height: { ideal: 480 },
          },
        });

        this.$nextTick(() => {
          if (this.$refs.videoElement) {
            this.$refs.videoElement.srcObject = this.stream;
          }
        });
      } catch (error) {
        console.error("Error switching camera:", error);
        this.errorMessage = "Tidak dapat mengganti kamera.";
        // Fallback to original facing mode
        this.currentFacingMode = this.currentFacingMode === "user" ? "environment" : "user";
      }
    },

    capturePhoto() {
      const video = this.$refs.videoElement;
      const canvas = this.$refs.canvasElement;

      if (!video || !canvas) return;

      const context = canvas.getContext("2d");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const file = new File([blob], "camera-photo.png", {
              type: "image/png",
            });
            this.setPhoto(file);
          }
        },
        "image/png",
        0.9,
      );

      this.closeCamera();
    },

    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.setPhoto(file);
      }
    },

    setPhoto(file) {
      // Validate file size (2MB limit)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        this.errorMessage = "Ukuran file terlalu besar. Maksimal 2MB.";
        return;
      }

      // Validate file type (PNG only)
      if (file.type !== "image/png") {
        this.errorMessage = "File harus berupa gambar PNG.";
        return;
      }

      this.errorMessage = null;

      // Create preview URL
      this.previewUrl = URL.createObjectURL(file);

      // Emit the file to parent component
      this.$emit("update:modelValue", file);
    },

    removePhoto() {
      if (this.previewUrl) {
        URL.revokeObjectURL(this.previewUrl);
        this.previewUrl = null;
      }

      // Clear file input
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = "";
      }

      this.$emit("update:modelValue", null);
      this.errorMessage = null;
    },

    async loadExistingPhoto() {
      if (!this.existingPhotoFilename || !this.apiKey) {
        return;
      }

      this.loadingExistingPhoto = true;
      this.clearExistingPhoto();

      try {
        const response = await fetch(`/api/biodata/generus/foto/${this.existingPhotoFilename}`, {
          headers: {
            'Authorization': `ApiKey ${this.apiKey}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        this.existingPhotoUrl = URL.createObjectURL(blob);
      } catch (error) {
        console.error('Error loading existing photo:', error);
        this.errorMessage = 'Gagal memuat foto yang sudah ada';
      } finally {
        this.loadingExistingPhoto = false;
      }
    },

    clearExistingPhoto() {
      if (this.existingPhotoUrl) {
        URL.revokeObjectURL(this.existingPhotoUrl);
        this.existingPhotoUrl = null;
      }
    },
  },
};
</script>

<style scoped>
.photo-upload-container {
  margin: 20px 0;
}

.section-title {
  font-weight: bold;
  margin-bottom: 15px;
  color: #2c4a3e;
}

.photo-preview {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  border: 2px solid #ddd;
  object-fit: cover;
}

.remove-photo-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.camera-section,
.file-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.camera-btn,
.file-upload-label {
  background-color: #2e5a35;
  color: #fff;
  border: none;
  padding: 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: background-color 0.3s ease;
  box-sizing: border-box;
  width: 100%;
  text-align: center;
  display: inline-block;
}

.camera-btn:hover,
.file-upload-label:hover {
  background-color: #3d7a47;
}

.camera-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.file-input {
  display: none;
}

.camera-warning {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.camera-content {
  background: white;
  border-radius: 15px;
  padding: 25px;
  max-width: 95vw;
  max-height: 95vh;
  overflow: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 2px solid #2e5a35;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.camera-controls-header {
  display: flex;
  gap: 10px;
  align-items: center;
}

.close-btn {
  background: #ff4444;
  color: white;
  border: none;
  font-size: 18px;
  cursor: pointer;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #ff6666;
  transform: scale(1.1);
}

.switch-camera-btn {
  background: #2e5a35;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.switch-camera-btn:hover {
  background: #3d7a47;
}

.camera-viewport {
  margin-bottom: 15px;
}

.camera-video {
  width: 100%;
  max-width: 640px;
  height: auto;
  border-radius: 8px;
}

.camera-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.capture-btn {
  background: #2c4a3e;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
}

.cancel-btn {
  background: #666;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
}

.error-message {
  color: #ff4444;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px;
  background: #ffe6e6;
  border-radius: 4px;
  border: 1px solid #ffcccc;
}

.loading-photo {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.existing-photo-label {
  position: absolute;
  bottom: -25px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #666;
  font-style: italic;
}
</style>
