#!/bin/bash

# Set up directory paths first
PROJECT_DIR="$(pwd)/backend"
APP_NAME="gnrs"
export SUPPORT_DIR="${PROJECT_DIR}/support"
export MAIN_DIR="${PROJECT_DIR}/main"
export DJANGO_DIR="${PROJECT_DIR}/django_auth"
export ROOT_DIR="${PROJECT_DIR}/.root_dir"

# Container names
export POD_NAME="${APP_NAME}_pod"
export POSTGRES_CONTAINER_NAME="${APP_NAME}_postgres"
export REDIS_CONTAINER_NAME="${APP_NAME}_redis"
export UVICORN_CONTAINER_NAME="${APP_NAME}_uvicorn"
export DJANGO_CONTAINER_NAME="${APP_NAME}_django"
export NGINX_CONTAINER_NAME="${APP_NAME}_nginx"
export PGADMIN_CONTAINER_NAME="${APP_NAME}_pgadmin"
export CFL_TUNNEL_CONTAINER_NAME="${APP_NAME}_cfltunnel"
export INTERACT_CONTAINER_NAME="${APP_NAME}_interact"
