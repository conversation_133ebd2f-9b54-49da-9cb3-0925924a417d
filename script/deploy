#!/bin/bash
# Check if command argument is provided
if [ -z "$1" ]; then
  echo "Error: App name is required"
  echo "Usage: $0 <app_name> <command>"
  exit 1
fi

if [ -z "$2" ]; then
  echo "Error: Command is required"
  echo "Usage: $0 <app_name> <command>"
  exit 1
fi

COMMAND="$1"

source ./setting

# Load environment configuration
ENV_FILE="${SUPPORT_DIR}/env.conf"
if [ ! -f "$ENV_FILE" ]; then
  echo "Creating environment configuration..."
  mkdir -p "$SUPPORT_DIR"
  cat >"$ENV_FILE" <<EOL
# Application settings
HOST_DOMAIN="api.var.my.id"
PORT1="8080"  # Nginx port
PORT2="8000"  # FastAPI port
PORT3="8001"  # Django port

# Database settings
POSTGRES_IMAGE="docker.io/library/postgres:16"
PYTHON_IMAGE="docker.io/library/python:latest"
REDIS_IMAGE="docker.io/valkey/valkey:latest"
NGINX_IMAGE="docker.io/library/nginx:latest"
PGADMIN_IMAGE="docker.io/dpage/pgadmin4:latest"
INTERACT_IMAGE="docker.io/library/python:latest"

# Path settings
ROOT_DIR="$HOME/.root_dir"
EOL
fi

# Generate secrets if not exists
if [ ! -f "${SUPPORT_DIR}/secrets.env" ]; then
  pg_user="${APP_NAME}_user"
  pg_pass=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9' | head -c 32)
  pg_db="${APP_NAME}_db"
  django_key=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9' | head -c 32)
  redis_pass=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9' | head -c 32)

  cat >"${SUPPORT_DIR}/secrets.env" <<SECRETS
# Database credentials
POSTGRES_USER=$pg_user
POSTGRES_PASSWORD=$pg_pass
POSTGRES_DB=$pg_db

# Django settings
DJANGO_SECRET_KEY=$django_key

# Redis settings
REDIS_PASSWORD=$redis_pass
SECRETS
  chmod 600 "${SUPPORT_DIR}/secrets.env"

  # ensure secrets.env exists to satisfy LSP
  if [ ! -e "${SUPPORT_DIR}/secrets.env" ]; then
    touch "${SUPPORT_DIR}/secrets.env"
  fi

fi

# Source environment configuration
# shellcheck source=/dev/null
if [ -f "$ENV_FILE" ]; then
  source "$ENV_FILE"
else
  echo "Error: Environment configuration file not found: $ENV_FILE"
  exit 1
fi

# Load secrets
set -a
if [ -f "${SUPPORT_DIR}/secrets.env" ]; then
  # shellcheck disable=SC1091
  source "${SUPPORT_DIR}/secrets.env"
else
  echo "Warning: secrets.env not found, skipping"
fi
set +a

# Cleanup function to unset environment variables
unset_secrets() {
  unset POSTGRES_USER POSTGRES_PASSWORD POSTGRES_DB DJANGO_SECRET_KEY REDIS_PASSWORD
}
trap unset_secrets EXIT

# Check if podman is installed
if ! command -v podman &>/dev/null; then
  echo "Error: podman is not installed"
  exit 1
fi

REQUIREMENTS_FILE="${SUPPORT_DIR}/requirements.txt"
MAIN_FILE="${MAIN_DIR}/main.py"
# DB_FILE="${MAIN_DIR}/db.py"
# SCHEMAS_FILE="${MAIN_DIR}/schemas.py"

rev() {
  echo "Creating Python virtual environment and installing requirements..."
  podman run --rm -v "$PROJECT_DIR:/app:z" -v "$ROOT_DIR:/root:z" "$PYTHON_IMAGE" bash -c "
    apt-get update && \
    apt-get install -y curl build-essential && \
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y && \
    . \$HOME/.cargo/env && \
    python -m venv /app/support/venv && \
    source /app/support/venv/bin/activate && \
    pip install --upgrade pip && \
    pip install -r /app/support/requirements.txt"
}

init() {
  # Check if project directory already exists
  if [ -d "$PROJECT_DIR" ]; then
    echo "Warning: Project directory already exists. Some files may be overwritten."
  fi

  echo "Creating project directories..."
  # Create all required directories
  for dir in \
    "$PROJECT_DIR" \
    "$SUPPORT_DIR" \
    "$MAIN_DIR" \
    "$MAIN_DIR/database" \
    "$MAIN_DIR/endpoints" \
    "$MAIN_DIR/core" \
    "$ROOT_DIR" \
    "$DJANGO_DIR" \
    "$DJANGO_DIR/static" \
    "$SUPPORT_DIR/db_data" \
    "$SUPPORT_DIR/redis_data" \
    "$SUPPORT_DIR/pgadmin" \
    "$SUPPORT_DIR/logs"; do
    if [ ! -d "$dir" ]; then
      mkdir -p "$dir"
    fi
  done

  # Create token file if it doesn't exist
  [ ! -f "$SUPPORT_DIR/token" ] && touch "$SUPPORT_DIR/token"

  # Set permissions for pgadmin directory
  chmod 777 "$SUPPORT_DIR/pgadmin"

  echo "Creating .gitignore..."
  cat >"$PROJECT_DIR/.gitignore" <<EOL
# Project specific
support/db_data/
support/redis_data/
support/pgadmin/
support/token
support/venv/
support/logs/
support/*.log
support/secrets.env
support/backups/
support/env.conf
support/secrets.env
django_auth/static/

# Python
__pycache__/
*.py[cod]
*\$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
ENV/
env/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
log/
logs/

# Database
*.sqlite3
*.db

# System Files
.DS_Store
Thumbs.db

# Alembic
versions/
EOL

  echo "Creating requirements.txt..."
  cat >"$REQUIREMENTS_FILE" <<EOL
fastapi
uvicorn
sqlmodel
pydantic[email]
psycopg2-binary
asyncpg
python-jose[cryptography]
passlib[bcrypt]
python-multipart
redis
fastapi-limiter
fastapi-cache2
python-dotenv
tenacity
alembic
SQLAlchemy-Utils
prometheus-client
structlog
Pillow
PyYAML
django
djangorestframework
django-cors-headers
django-environ
djangorestframework-simplejwt
requests
gunicorn
django-redis
EOL

  rev

  # Initialize Django project
  echo "Initializing Django project..."
  sleep 2
  podman run -it --rm -v "$PROJECT_DIR:/app:z" "$PYTHON_IMAGE" bash -c "
    source /app/support/venv/bin/activate && \
    cd /app/django_auth && \
    django-admin startproject auth_project . && \
    python manage.py startapp authentication"

  # Wait a moment to ensure files are created
  sleep 10

  # Create Django settings
  echo "Creating Django settings..."
  cat >"$DJANGO_DIR/auth_project/settings.py" <<EOL
import os
from pathlib import Path
from datetime import timedelta

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', '${DJANGO_SECRET_KEY}')

DEBUG = True

ALLOWED_HOSTS = ['*']

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'authentication',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'auth_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'auth_project.wsgi.application'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('POSTGRES_DB'),
        'USER': os.getenv('POSTGRES_USER'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD'),
        'HOST': os.getenv('POSTGRES_CONTAINER_NAME'),
        'PORT': '5432',
    }
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

STATIC_URL = 'auth/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
}

SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer',),
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{os.getenv('REDIS_CONTAINER_NAME', 'localhost')}:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

CORS_ALLOW_ALL_ORIGINS = True
EOL

  echo "Creating Django authentication views..."
  cat >"$DJANGO_DIR/authentication/views.py" <<EOL
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.cache import cache
from django.views.decorators.cache import cache_page

@api_view(['POST'])
def login(request):
    username = request.data.get('username')
    password = request.data.get('password')

    user = authenticate(username=username, password=password)
    if user:
        refresh = RefreshToken.for_user(user)
        return Response({
            'access': str(refresh.access_token),
            'refresh': str(refresh),
        })
    return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_token(request):
    user_id = request.user.id
    cache_key = f'token_valid_{user_id}'

    # Check if the result is cached
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        return Response({'status': 'valid', 'cached': True})

    # If not cached, perform verification logic here
    # For simplicity, we're just caching the fact that the token is valid
    cache.set(cache_key, True, timeout=300)  # Cache for 5 minutes

    return Response({'status': 'valid', 'cached': False})

@api_view(['GET'])
@cache_page(60 * 15)  # Cache this view for 15 minutes
def cached_view(request):
    # This is just an example of a view that uses Django's cache_page decorator
    return Response({'message': 'This response is cached for 15 minutes'})
EOL

  echo "Creating Django project URLs..."
  cat >"$DJANGO_DIR/auth_project/urls.py" <<EOL
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('auth/', include('authentication.urls')),
]
EOL

  echo "Creating Django URLs..."
  cat >"$DJANGO_DIR/authentication/urls.py" <<EOL
from django.urls import path
from . import views

urlpatterns = [
    path('login/', views.login, name='login'),
    path('verify/', views.verify_token, name='verify'),
    path('cached/', views.cached_view, name='cached_view'),
]
EOL

  echo "Creating Django run script..."
  cat >"$DJANGO_DIR/run.sh" <<EOL
#!/bin/bash
source /app/support/venv/bin/activate
cd /app/django_auth

# Apply migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser if it doesn't exist
python manage.py shell <<EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin')
EOF

# Collect static files
python manage.py collectstatic --noinput

# Run with gunicorn
exec gunicorn auth_project.wsgi:application \
    --bind 0.0.0.0:8001 \
    --workers 2 \
    --threads 2 \
    --worker-class gthread \
    --worker-tmp-dir /dev/shm \
    --access-logfile /app/support/logs/gunicorn-access.log \
    --error-logfile /app/support/logs/gunicorn-error.log \
    --capture-output \
    --enable-stdio-inheritance \
    --reload
EOL

  chmod 755 "$DJANGO_DIR/run.sh"

  # Create core directory and move schemas.py there
  echo "Creating core/schemas.py..."
  cat >"$MAIN_DIR/core/schemas.py" <<EOL
from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime

class TodoBase(SQLModel):
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    completed: bool = False

class Todo(TodoBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class TodoCreate(TodoBase):
    pass

class TodoRead(TodoBase):
    id: int
    created_at: datetime
    updated_at: datetime

class TodoUpdate(SQLModel):
    title: Optional[str] = None
    description: Optional[str] = None
    completed: Optional[bool] = None
EOL

  # Create database directory and move db.py there
  echo "Creating database/db.py..."
  cat >"$MAIN_DIR/database/db.py" <<EOL
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator, AsyncGenerator
import os
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration with fallbacks
POSTGRES_CONTAINER = os.getenv('POSTGRES_CONTAINER_NAME', 'localhost')
POSTGRES_USER = os.getenv('POSTGRES_USER', '${POSTGRES_USER}')
POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', '${POSTGRES_PASSWORD}')
POSTGRES_DB = os.getenv('POSTGRES_DB', '${POSTGRES_DB}')
POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')

# Connection URLs for both sync and async engines
SYNC_DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_CONTAINER}:{POSTGRES_PORT}/{POSTGRES_DB}"
ASYNC_DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_CONTAINER}:{POSTGRES_PORT}/{POSTGRES_DB}"

# Engine configuration
POOL_SIZE = int(os.getenv('POOL_SIZE', '5'))
MAX_OVERFLOW = int(os.getenv('MAX_OVERFLOW', '10'))
POOL_TIMEOUT = int(os.getenv('POOL_TIMEOUT', '30'))
POOL_RECYCLE = int(os.getenv('POOL_RECYCLE', '1800'))  # 30 minutes
CONNECT_TIMEOUT = int(os.getenv('CONNECT_TIMEOUT', '10'))  # 10 seconds

# Create engines with optimized configurations
engine = create_engine(
    SYNC_DATABASE_URL,
    poolclass=QueuePool,
    pool_size=POOL_SIZE,
    max_overflow=MAX_OVERFLOW,
    pool_timeout=POOL_TIMEOUT,
    pool_recycle=POOL_RECYCLE,
    pool_pre_ping=True,
    connect_args={
        "connect_timeout": CONNECT_TIMEOUT,
        "application_name": "fastapi_app"  # Helps identify connections in pg_stat_activity
    }
)

async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    pool_size=POOL_SIZE,
    max_overflow=MAX_OVERFLOW,
    pool_timeout=POOL_TIMEOUT,
    pool_recycle=POOL_RECYCLE,
    pool_pre_ping=True,
    connect_args={
        "timeout": CONNECT_TIMEOUT,
        "application_name": "fastapi_app_async"
    }
)

@contextmanager
def get_db() -> Generator[Session, None, None]:
    """
    Synchronous database session generator with retry mechanism.
    """
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    def _get_session():
        try:
            db = Session(engine)
            return db
        except Exception as e:
            logger.error(f"Failed to create database session: {e}")
            raise

    db = _get_session()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Asynchronous database session generator with retry mechanism.
    """
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    async def _get_async_session():
        try:
            async with AsyncSession(async_engine) as session:
                return session
        except Exception as e:
            logger.error(f"Failed to create async database session: {e}")
            raise

    try:
        async_session = await _get_async_session()
        yield async_session
    except Exception as e:
        logger.error(f"Async database session error: {e}")
        await async_session.rollback()
        raise
    finally:
        await async_session.close()

# Helper function to check database health
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def check_database_health() -> bool:
    """
    Check if database is responsive and healthy.
    Returns True if database is healthy, raises exception otherwise.
    """
    try:
        async with async_engine.connect() as conn:
            await conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        raise

# Create all tables defined in SQLModel metadata
def init_db():
    """
    Initialize database tables with error handling.
    """
    try:
        SQLModel.metadata.create_all(engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise
EOL

  # Create endpoints directory and move todo endpoints there
  echo "Creating endpoints/todos.py..."
  cat >"$MAIN_DIR/endpoints/todos.py" <<EOL
from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import Session, select
from typing import List
from core.schemas import Todo, TodoCreate, TodoRead
from database.db import get_db
from core.auth import verify_token

router = APIRouter()

@router.post("/", response_model=TodoRead, dependencies=[Depends(verify_token)])
async def create_todo(todo: TodoCreate, db: Session = Depends(get_db)):
    db_todo = Todo.from_orm(todo)
    db.add(db_todo)
    db.commit()
    db.refresh(db_todo)
    return db_todo

@router.get("/", response_model=List[TodoRead], dependencies=[Depends(verify_token)])
async def list_todos(db: Session = Depends(get_db)):
    todos = db.exec(select(Todo)).all()
    return todos

@router.get("/{todo_id}", response_model=TodoRead, dependencies=[Depends(verify_token)])
async def get_todo(todo_id: int, db: Session = Depends(get_db)):
    todo = db.get(Todo, todo_id)
    if todo is None:
        raise HTTPException(status_code=404, detail="Todo not found")
    return todo
EOL

  # Create core/auth.py for authentication
  echo "Creating core/auth.py..."
  cat >"$MAIN_DIR/core/auth.py" <<EOL
from fastapi import HTTPException, Header
import os
import requests

async def verify_token(authorization: str = Header(None)):
    if not authorization:
        raise HTTPException(status_code=401, detail="No token provided")

    try:
        response = requests.post(
            f"http://{os.getenv('DJANGO_CONTAINER_NAME')}:8001/auth/verify/",
            headers={"Authorization": authorization}
        )
        if response.status_code != 200:
            raise HTTPException(status_code=401, detail="Invalid token")
    except requests.RequestException:
        raise HTTPException(status_code=503, detail="Authentication service unavailable")
EOL

  # Create main.py with updated imports
  echo "Creating main.py..."
  cat >"$MAIN_FILE" <<EOL
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi_limiter import FastAPILimiter
from redis import asyncio as aioredis
from sqlmodel import SQLModel
import os
import uvicorn
from database.db import engine
from endpoints import todos

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup():
    try:
        # Create database tables
        SQLModel.metadata.create_all(engine)

        # Initialize Redis using container name
        redis = aioredis.from_url(f"redis://{os.getenv('REDIS_CONTAINER_NAME', 'localhost')}:6379", encoding="utf8", decode_responses=True)
        await FastAPILimiter.init(redis)
        FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    except Exception as e:
        print(f"Startup error: {e}")
        raise

# Include routers
app.include_router(todos.router, prefix="/todos", tags=["todos"])

@app.get("/")
async def root():
    return {"message": "Hello World"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
EOL

  # Create __init__.py files for Python packages
  touch "$MAIN_DIR/core/__init__.py"
  touch "$MAIN_DIR/database/__init__.py"
  touch "$MAIN_DIR/endpoints/__init__.py"

  echo "Creating uvicorn.sh..."
  cat >"$SUPPORT_DIR/uvicorn.sh" <<EOL
#!/bin/bash
source /app/support/venv/bin/activate
cd /app/main

# Run uvicorn with simplified logging
exec uvicorn main:app \
  --reload \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --log-level debug \
  --access-log \
  --use-colors \
  --reload-dir /app/main
EOL

  chmod 755 "$SUPPORT_DIR/uvicorn.sh"

  echo "Creating nginx.conf..."
  cat >"$SUPPORT_DIR/nginx.conf" <<EOL
server {
    listen $PORT1;
    server_name 127.0.0.1;

    location /auth/ {
        proxy_pass http://127.0.0.1:8001/auth/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /admin/ {
        proxy_pass http://127.0.0.1:8001/admin/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /auth/static/ {
        alias /www/django_auth/staticfiles/;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOL

  echo "Initializing Alembic..."
  podman run --rm -v "$PROJECT_DIR:/app:z" -w /app "$PYTHON_IMAGE" bash -c "
    source /app/support/venv/bin/activate && \
    cd /app/main && \
    pip install alembic && \
    alembic init migrations"

  # Update alembic.ini
  ESCAPED_PASSWORD=$(printf '%s\n' "$POSTGRES_PASSWORD" | sed -e 's/[\/&]/\\&/g')
  sed -i "s|sqlalchemy.url = driver://user:pass@localhost/dbname|sqlalchemy.url = postgresql://${POSTGRES_USER}:${ESCAPED_PASSWORD}@${POSTGRES_CONTAINER_NAME}:5432/${POSTGRES_DB}|g" "$MAIN_DIR/alembic.ini"

  # Update env.py with correct imports and configuration
  cat >"$MAIN_DIR/migrations/env.py" <<EOT
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
import os, sys, importlib, glob
from pathlib import Path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import all models - dynamically import all Python files that might contain models
from sqlmodel import SQLModel

# Dynamically import all Python files in the main directory
main_dir = Path(os.path.dirname(os.path.dirname(__file__)))
for file_path in glob.glob(str(main_dir / "*.py")):
    if not file_path.endswith('env.py'):  # Skip this env.py file
        module_name = os.path.splitext(os.path.basename(file_path))[0]
        try:
            importlib.import_module(module_name)
        except Exception as e:
            print(f"Warning: Could not import {module_name}: {e}")

# this is the Alembic Config object
config = context.config

# Interpret the config file for Python logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set SQLModel's metadata for Alembic
target_metadata = SQLModel.metadata

def get_url():
    """Get database URL from environment variables."""
    user = os.getenv('POSTGRES_USER')
    password = os.getenv('POSTGRES_PASSWORD')
    host = os.getenv('POSTGRES_CONTAINER_NAME')
    db = os.getenv('POSTGRES_DB')
    return f"postgresql://{user}:{password}@{host}:5432/{db}"

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # Enhanced migration options
        compare_type=True,  # Detect column type changes
        compare_server_default=True,  # Detect default value changes
        render_as_batch=True,  # Better handling of ALTER TABLE operations
        include_schemas=True,  # Include schema-level operations
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    configuration = config.get_section(config.config_ini_section)
    if configuration is not None:
        configuration["sqlalchemy.url"] = get_url()

    connectable = engine_from_config(
        configuration or {},
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            # Enhanced migration options
            compare_type=True,  # Detect column type changes
            compare_server_default=True,  # Detect default value changes
            render_as_batch=True,  # Better handling of ALTER TABLE operations
            include_schemas=True,  # Include schema-level operations
            include_name=True,  # Consider column/table name changes
        )

        with context.begin_transaction():
            context.run_migrations()

def run_migrations():
    """Run migrations in either 'online' or 'offline' mode."""
    try:
        if context.is_offline_mode():
            run_migrations_offline()
        else:
            run_migrations_online()
    except Exception as e:
        print(f"Error during migration: {e}")
        raise

if __name__ == "__main__":
    run_migrations()
EOT
}

stop() {
  echo "Stopping and removing pod..."
  podman pod stop "$POD_NAME" || true
  podman pod rm "$POD_NAME" || true
}

run_postgres() {
  echo "Starting PostgreSQL container..."
  podman run -d --pod "$POD_NAME" --name "$POSTGRES_CONTAINER_NAME" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -v "$SUPPORT_DIR/db_data:/var/lib/postgresql/data:z" \
    "$POSTGRES_IMAGE"
}

wait_for_postgres() {
  echo "Waiting for PostgreSQL to be ready..."
  for i in {1..30}; do
    if podman exec -it "$POSTGRES_CONTAINER_NAME" pg_isready -U "$POSTGRES_USER" &>/dev/null; then
      echo "PostgreSQL is ready."
      return 0
    fi
    echo "Waiting for PostgreSQL... ($i/30)"
    sleep 2
  done
  echo "PostgreSQL did not become ready in time."
  return 1
}

run_redis() {
  echo "Starting Redis container..."
  podman run -d --pod "$POD_NAME" --name "$REDIS_CONTAINER_NAME" \
    -v "$SUPPORT_DIR/redis_data:/data:z" \
    "$REDIS_IMAGE" --loglevel warning
}

run_nginx() {
  echo "Starting Nginx container..."
  podman run -d --pod "$POD_NAME" --name "$NGINX_CONTAINER_NAME" \
    -v "$SUPPORT_DIR/nginx.conf:/etc/nginx/conf.d/default.conf:ro" \
    -v "$DJANGO_DIR/static:/www/django_auth/staticfiles:ro" \
    "$NGINX_IMAGE"
}

run_cfl_tunnel() {
  if [ ! -s "$SUPPORT_DIR/token" ]; then
    echo "Error: Cloudflare tunnel token is empty. Please add your token to $SUPPORT_DIR/token"
    return 1
  fi

  echo "Starting Cloudflare tunnel..."
  podman run -d --pod "$POD_NAME" --name "$CFL_TUNNEL_CONTAINER_NAME" \
    docker.io/cloudflare/cloudflared:latest tunnel --no-autoupdate run \
    --token "$(cat "$SUPPORT_DIR/token")"
}

run_uvicorn() {
  echo "Starting Uvicorn container..."
  podman run -d --pod "$POD_NAME" --name "$UVICORN_CONTAINER_NAME" \
    -v "$PROJECT_DIR:/app:z" \
    -e "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME" \
    -e "REDIS_CONTAINER_NAME=$REDIS_CONTAINER_NAME" \
    -e "DJANGO_CONTAINER_NAME=$DJANGO_CONTAINER_NAME" \
    -e "POSTGRES_USER=$POSTGRES_USER" \
    -e "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" \
    -e "POSTGRES_DB=$POSTGRES_DB" \
    -e "DJANGO_SECRET_KEY=$RANDOM_KEY" \
    -w /app \
    "$PYTHON_IMAGE" ./support/uvicorn.sh
}

run_interact() {
  echo "Starting interactive container..."
  podman run -d --pod "$POD_NAME" --name "$INTERACT_CONTAINER_NAME" \
    -v "$ROOT_DIR:/root:z" \
    -v "$PROJECT_DIR:/app:z" \
    -e "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME" \
    -e "REDIS_CONTAINER_NAME=$REDIS_CONTAINER_NAME" \
    -e "DJANGO_CONTAINER_NAME=$DJANGO_CONTAINER_NAME" \
    -e "POSTGRES_USER=$POSTGRES_USER" \
    -e "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" \
    -e "POSTGRES_DB=$POSTGRES_DB" \
    -e "DJANGO_SECRET_KEY=$DJANGO_SECRET_KEY" \
    -w /app \
    "$INTERACT_IMAGE" bash -c "sleep infinity"
}

run_cmd() {
  podman exec -it "$INTERACT_CONTAINER_NAME" \
    bash -c \
    "source /app/support/venv/bin/activate; $*"
}

pg() {
  echo "Starting pgAdmin container..."
  podman run -d --pod "$POD_NAME" --name "$PGADMIN_CONTAINER_NAME" \
    -e "PGADMIN_DEFAULT_EMAIL=<EMAIL>" \
    -e "PGADMIN_DEFAULT_PASSWORD=SuperSecret" \
    -e "PGADMIN_LISTEN_PORT=5050" \
    -v "$SUPPORT_DIR/pgadmin:/var/lib/pgadmin:z" \
    "$PGADMIN_IMAGE"
}

db() {
  echo "Running database migrations..."

  # Check if the containers are running
  if ! podman container exists "$INTERACT_CONTAINER_NAME" || [ "$(podman container inspect -f '{{.State.Running}}' "$INTERACT_CONTAINER_NAME")" != "true" ]; then
    echo "Interactive container is not running. Please start the services first."
    return 1
  fi

  if ! podman container exists "$POSTGRES_CONTAINER_NAME" || [ "$(podman container inspect -f '{{.State.Running}}' "$POSTGRES_CONTAINER_NAME")" != "true" ]; then
    echo "PostgreSQL container is not running. Please start the services first."
    return 1
  fi

  # Wait for PostgreSQL to be ready
  echo "Checking PostgreSQL connection..."
  if ! podman exec -it "$POSTGRES_CONTAINER_NAME" pg_isready -U "$POSTGRES_USER"; then
    echo "PostgreSQL is not ready. Please check the container status."
    return 1
  fi

  # Run Django migrations
  echo "Running Django migrations..."
  podman exec -it \
    -e POSTGRES_CONTAINER_NAME="$POSTGRES_CONTAINER_NAME" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    "$INTERACT_CONTAINER_NAME" bash -c "
      set -e
      echo 'Activating virtual environment...'
      source /app/support/venv/bin/activate
      cd /app/django_auth

      echo 'Making Django migrations...'
      python manage.py makemigrations
      python manage.py makemigrations authentication

      echo 'Applying Django migrations...'
      python manage.py migrate
      python manage.py migrate authentication

      echo 'Django migrations completed successfully.'
    " || {
    echo "Django migration failed."
    return 1
  }

  # Run FastAPI/Alembic migrations
  echo "Running FastAPI migrations..."
  podman exec -it \
    -e POSTGRES_CONTAINER_NAME="$POSTGRES_CONTAINER_NAME" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    "$INTERACT_CONTAINER_NAME" bash -c "
      set -e
      echo 'Activating virtual environment...'
      source /app/support/venv/bin/activate
      cd /app/

      if [ ! -d 'migrations/versions' ]; then
        echo 'Creating migrations directory...'
        mkdir -p migrations/versions
      fi

      echo 'Creating new Alembic migration...'
      alembic -c alembic.ini revision --autogenerate -m 'auto_migration'

      echo 'Upgrading database to latest version...'
      alembic -c alembic.ini upgrade head

      echo 'FastAPI migrations completed successfully.'
    " || {
    echo "FastAPI migration failed."
    return 1
  }

  echo "All database migrations completed successfully."
}

pod_create() {
  echo "Creating pod..."
  podman pod create --name "$POD_NAME" --restart always --network bridge
}

run_django() {
  echo "Starting Django container..."
  podman run -d --pod "$POD_NAME" --name "$DJANGO_CONTAINER_NAME" \
    -v "$PROJECT_DIR:/app:z" \
    -e "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME" \
    -e "POSTGRES_USER=$POSTGRES_USER" \
    -e "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" \
    -e "POSTGRES_DB=$POSTGRES_DB" \
    -w /app \
    "$PYTHON_IMAGE" ./django_auth/run.sh
}

wait_for_django() {
  echo "Waiting for Django to be ready..."
  for i in {1..60}; do
    if podman exec -it "$DJANGO_CONTAINER_NAME" curl -s http://127.0.0.1:8001/auth/login/ &>/dev/null; then
      echo "Django is ready."
      return 0
    fi
    sleep 2
  done
  echo "Django did not become ready in time."
  exit 1
}

esse() {
  run_postgres || return 1
  wait_for_postgres
  run_redis || return 1
  run_django || return 1
  wait_for_django
  run_uvicorn || return 1
  run_nginx || return 1
  run_cfl_tunnel || return 1
  run_interact || return 1
}

start() {
  pod_create
  esse
  pg
}

cek() {
  if podman pod exists "$POD_NAME"; then
    if [ "$(podman pod ps --filter name="$POD_NAME" --format "{{.Status}}" | awk '{print $1}')" = "Running" ]; then
      for container in "${POSTGRES_CONTAINER_NAME}" "${REDIS_CONTAINER_NAME}" "${DJANGO_CONTAINER_NAME}" "${UVICORN_CONTAINER_NAME}" "${NGINX_CONTAINER_NAME}" "${CFL_TUNNEL_CONTAINER_NAME}" "${INTERACT_CONTAINER_NAME}" "${PGADMIN_CONTAINER_NAME}"; do
        if [ "$(podman ps --filter name="$container" --format "{{.Status}}" | awk '{print $1}')" != "Up" ]; then
          echo "Container $container is not running. Restarting..."
          podman start "$container" || {
            echo "Failed to restart $container"
            return 1
          }
        fi
      done
      echo "All containers are running."
    else
      echo "Pod is not running. Starting pod..."
      podman pod start "$POD_NAME" || {
        echo "Failed to start pod"
        return 1
      }
    fi
  else
    echo "Pod does not exist. Creating and starting..."
    start
  fi
}

create_user() {
  # Check if pod exists and is running
  if ! podman pod exists "$POD_NAME"; then
    echo "Pod does not exist. Please start the services first using 'start' command"
    return 1
  fi

  if [ "$(podman pod ps --filter name="$POD_NAME" --format "{{.Status}}" | awk '{print $1}')" != "Running" ]; then
    echo "Pod is not running. Please start the services first using 'start' command"
    return 1
  fi

  # Check if PostgreSQL container is running and ready
  if ! podman exec -it "$POSTGRES_CONTAINER_NAME" pg_isready -U "$POSTGRES_USER" &>/dev/null; then
    echo "PostgreSQL is not ready. Attempting to restart..."
    podman restart "$POSTGRES_CONTAINER_NAME"

    # Wait for PostgreSQL to be ready
    for i in {1..30}; do
      if podman exec -it "$POSTGRES_CONTAINER_NAME" pg_isready -U "$POSTGRES_USER" &>/dev/null; then
        echo "PostgreSQL is now ready."
        break
      fi
      if [ "$i" -eq 30 ]; then
        echo "PostgreSQL failed to become ready. Please check the container logs:"
        podman logs "$POSTGRES_CONTAINER_NAME"
        return 1
      fi
      echo "Waiting for PostgreSQL to become ready... ($i/30)"
      sleep 2
    done
  fi

  # Ensure Django container is running and ready
  if ! podman container exists "$DJANGO_CONTAINER_NAME" || [ "$(podman container inspect -f '{{.State.Running}}' "$DJANGO_CONTAINER_NAME")" != "true" ]; then
    echo "Django container is not running. Attempting to restart..."
    podman restart "$DJANGO_CONTAINER_NAME"
    sleep 5
  fi

  # Export required environment variables
  export POSTGRES_CONTAINER_NAME="$POSTGRES_CONTAINER_NAME"
  export POSTGRES_USER="$POSTGRES_USER"
  export POSTGRES_PASSWORD="$POSTGRES_PASSWORD"
  export POSTGRES_DB="$POSTGRES_DB"

  # Use the interactive container to create a Django user with proper environment setup
  echo "Creating a new Django user..."
  podman exec -it \
    -e POSTGRES_CONTAINER_NAME="$POSTGRES_CONTAINER_NAME" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    "$INTERACT_CONTAINER_NAME" bash -c "
    source /app/support/venv/bin/activate
    cd /app/django_auth
    python manage.py createsuperuser
  "
}

# Check if the command exists as a function
if [ "$COMMAND" = "run_cmd" ]; then
  # If command is run_cmd, pass all remaining arguments to it
  shift 2 # Remove APP_NAME and COMMAND
  run_cmd "$@"
elif [ "$(type -t "$COMMAND")" = "function" ]; then
  # Execute other commands normally
  $COMMAND
else
  echo "Error: Unknown command '$COMMAND'"
  echo "Available commands: init, start, stop, db, pg, cek, create_user, run_cmd"
  exit 1
fi
