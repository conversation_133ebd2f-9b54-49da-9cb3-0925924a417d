#!/bin/bash
# Migration script from Python backend to Go backend
# This script helps transition from the existing Python FastAPI backend to the new Go backend

APP_NAME="${1:-besb}"

echo "=========================================="
echo "Migration from Python to Go Backend"
echo "App: $APP_NAME"
echo "=========================================="

# Check if Python backend is running
echo "1. Checking current Python backend status..."
if ./script/faster "$APP_NAME" status | grep -q "Running"; then
    echo "✅ Python backend is currently running"
    PYTHON_RUNNING=true
else
    echo "ℹ️  Python backend is not running"
    PYTHON_RUNNING=false
fi

# Check if Go backend is already running
echo -e "\n2. Checking Go backend status..."
if ./script/go-deploy "$APP_NAME" status | grep -q "Up"; then
    echo "ℹ️  Go backend is already running"
    GO_RUNNING=true
else
    echo "ℹ️  Go backend is not running"
    GO_RUNNING=false
fi

# Backup current state
echo -e "\n3. Creating backup of current state..."
BACKUP_DIR="$HOME/migration_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup database if Python backend is running
if [ "$PYTHON_RUNNING" = true ]; then
    echo "Creating database backup..."
    ./script/faster "$APP_NAME" run_cmd "pg_dump -U besb_user besb_db" > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || {
        echo "⚠️  Database backup failed, but continuing..."
    }
fi

# Copy photo files if they exist
if [ -d "$HOME/projects/api_${APP_NAME}/main/fotos" ]; then
    echo "Backing up photo files..."
    cp -r "$HOME/projects/api_${APP_NAME}/main/fotos" "$BACKUP_DIR/" || {
        echo "⚠️  Photo backup failed, but continuing..."
    }
fi

echo "✅ Backup created at: $BACKUP_DIR"

# Initialize Go backend if not already done
echo -e "\n4. Initializing Go backend..."
if [ ! -d "$HOME/projects/go_${APP_NAME}" ]; then
    ./script/go-deploy "$APP_NAME" init
    echo "✅ Go backend initialized"
else
    echo "ℹ️  Go backend already initialized"
fi

# Copy photos to Go backend directory
echo -e "\n5. Migrating photo files..."
GO_FOTOS_DIR="$HOME/projects/go_${APP_NAME}/support/fotos"
if [ -d "$HOME/projects/api_${APP_NAME}/main/fotos" ] && [ "$(ls -A "$HOME/projects/api_${APP_NAME}/main/fotos" 2>/dev/null)" ]; then
    mkdir -p "$GO_FOTOS_DIR"
    cp -r "$HOME/projects/api_${APP_NAME}/main/fotos/"* "$GO_FOTOS_DIR/" 2>/dev/null || true
    echo "✅ Photo files migrated to Go backend"
else
    echo "ℹ️  No photo files to migrate"
fi

# Perform the migration
echo -e "\n6. Performing migration..."
echo "This will:"
echo "  - Stop the Python backend (if running)"
echo "  - Start the Go backend"
echo "  - Test the new deployment"
echo ""
read -p "Continue with migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled"
    exit 0
fi

# Stop Python backend
if [ "$PYTHON_RUNNING" = true ]; then
    echo "Stopping Python backend..."
    ./script/faster "$APP_NAME" stop
    echo "✅ Python backend stopped"
fi

# Start Go backend
echo "Starting Go backend..."
if ./script/go-deploy "$APP_NAME" start; then
    echo "✅ Go backend started successfully"
else
    echo "❌ Go backend failed to start"
    echo "Rolling back to Python backend..."
    ./script/faster "$APP_NAME" start
    exit 1
fi

# Wait for Go backend to be ready
echo "Waiting for Go backend to be ready..."
sleep 10

# Test the new deployment
echo -e "\n7. Testing new Go backend deployment..."
if ./script/test-go-deployment "$APP_NAME"; then
    echo "✅ Go backend is working correctly"
else
    echo "❌ Go backend tests failed"
    echo "Rolling back to Python backend..."
    ./script/go-deploy "$APP_NAME" stop
    ./script/faster "$APP_NAME" start
    exit 1
fi

# Verify API compatibility
echo -e "\n8. Verifying API compatibility..."
BASE_URL="http://localhost:8080"

# Test a few key endpoints
echo "Testing key endpoints..."
endpoints=(
    "/"
    "/health"
    "/wilayah/provinces"
    "/data/hobi"
    "/data/kelas-sekolah"
)

for endpoint in "${endpoints[@]}"; do
    if curl -s -f "$BASE_URL$endpoint" > /dev/null; then
        echo "✅ $endpoint"
    else
        echo "❌ $endpoint"
    fi
done

echo -e "\n=========================================="
echo "Migration Summary"
echo "=========================================="
echo "✅ Python backend stopped"
echo "✅ Go backend started and tested"
echo "✅ Database preserved (same database used)"
echo "✅ Photo files migrated"
echo "✅ API endpoints verified"
echo ""
echo "Migration completed successfully!"
echo ""
echo "The Go backend is now serving on the same ports:"
echo "  - External access: http://localhost:8080"
echo "  - Direct backend: http://localhost:8000"
echo ""
echo "Backup location: $BACKUP_DIR"
echo ""
echo "Next steps:"
echo "1. Monitor logs: ./script/go-deploy $APP_NAME logs"
echo "2. Test authentication with existing tokens"
echo "3. Verify file uploads work correctly"
echo "4. Update any monitoring/alerting systems"
echo ""
echo "To rollback if needed:"
echo "  ./script/go-deploy $APP_NAME stop"
echo "  ./script/faster $APP_NAME start"
