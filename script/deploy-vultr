#!/bin/bash

if [ -z "$1" ]; then
  echo "Usage: $0 <instance-name>"
  exit 1
fi

instance_name="$1"

export SSH_OPTIONS="-o StrictHostKeyChecking=accept-new"
export GIT_SSH_COMMAND="ssh $SSH_OPTIONS"

# ----- Configurable paths/users -----
LOCAL_USER="${LOCAL_USER:-$(whoami)}"
LOCAL_HOME="${LOCAL_HOME:-/home/<USER>"
REMOTE_USER="${REMOTE_USER:-$LOCAL_USER}"
REMOTE_HOME="/home/<USER>"
SERVE_DIR="${SERVE_DIR:-$LOCAL_HOME/serve}"
PROJECTS_ROOT="${PROJECTS_ROOT:-$LOCAL_HOME/projects}"
DEPLOY_SUPPORT_DIR="$PROJECTS_ROOT/api_${instance_name}/.deploy-support"
ESKRIM_BIN="${ESKRIM_BIN:-$LOCAL_HOME/eskrim/bin}"
# ------------------------------------

alias ssh='ssh $SSH_OPTIONS'
alias sshfs="sshfs -o StrictHostKeyChecking=accept-new"

# Initial lookup of instance ID and IP
id=$(vultr-cli instance list | grep "$instance_name" | awk '{print $1}')
ip=$(vultr-cli instance list | grep "$instance_name" | awk '{print $2}')

# Function to refresh ID and IP after creation
refresh_ip() {
  id=$(vultr-cli instance list | grep "$instance_name" | awk '{print $1}')
  ip=$(vultr-cli instance list | grep "$instance_name" | awk '{print $2}')
}

# -p vc2-1c-1gb \
init() {
  vultr-cli instance create \
    -l "$1" \
    -r sgp \
    -p vhp-1c-1gb-amd \
    --os 535 \
    -s 6dd16144-6bb6-4aed-aee5-552d627b47dd \
    --script-id f77d883d-3d14-4118-94df-b1b59c699ec3
}

waitin() {
  local total=$1
  local spin='-\|/'
  for ((i = 1; i <= total; i++)); do
    local idx=$((i % ${#spin}))
    printf "\r[ %c ] Waiting... %2ds remaining " "${spin:idx:1}" $((total - i))
    sleep 1
  done
  echo
}

sfs() {
  local remote_path="${1:-$REMOTE_HOME}"
  local mount_point="${2:-$SERVE_DIR}"
  # ensure mount point exists
  mkdir -p "$mount_point"
  umount -l "$mount_point" >/dev/null 2>&1
  sshfs -p 22 \
    root@"$ip":"$remote_path" "$mount_point" \
    -o "_netdev,reconnect,users,idmap=user,follow_symlinks,identityfile=$LOCAL_HOME/.ssh/id_ed25519,allow_other,default_permissions,uid=$(id -u),gid=$(id -g)"
}

cop() {
  cp -f "$DEPLOY_SUPPORT_DIR/$1" "$SERVE_DIR/$1"
}

deploy() {
  # mount, copy a single file, then unmount
  sfs "$REMOTE_HOME" "$SERVE_DIR"
  sleep 5
  cop "$1"
  ssh root@"$ip" -t "chown $REMOTE_USER:$REMOTE_USER $REMOTE_HOME/$1"
  umount -l "$SERVE_DIR" >/dev/null 2>&1
}

if [ -z "$id" ]; then
  echo "No instance with name '$instance_name' was found."
  echo "Initializing..."
  init "$instance_name"

  echo "Waiting for instance to be assigned an IP..."
  # Poll until refresh_ip finds a non-empty IP
  until refresh_ip && [ -n "$ip" ] && [ "$ip" != "0.0.0.0" ]; do
    echo -n "."
    sleep 5
  done

  echo "Instance ready at $ip."

  echo "Waiting for instance to prepare..."

  waitin 160

  echo "Setting init.sh..."
  deploy init.sh

  echo "Running init.sh..."
  ssh root@"$ip" -t "bash $REMOTE_HOME/init.sh && reboot"

  waitin 25
  echo -e "\rInstance preparation complete!          "

  echo "Setting token..."
  deploy token

  echo "Setting run.sh..."
  deploy run.sh

  echo "Setting secrets.env..."
  deploy secrets.env

  echo "Setting env.conf..."
  deploy env.conf

fi

# Loop until rev is successfully checked
while true; do
  echo "Checking revision..."
  rev=$(ssh "$REMOTE_USER"@"$ip" -t cat "$REMOTE_HOME/.rev" 2>/dev/null || echo "")

  if [ -z "$rev" ]; then
    echo "Waiting for revision to be checked..."
    echo "Rev is empty, waiting..."
    waitin 25
  else
    echo "Revision is 1, running bacres..."
    ssh root@"$ip" -t "systemctl disable --now cek"
    ssh "$REMOTE_USER"@"$ip" -t "$REMOTE_HOME/belomtau/faster $instance_name stop"
    "$ESKRIM_BIN/bacres" "${instance_name}" res
    waitin 10
    ssh root@"$ip" -t "systemctl enable --now cek"
    break
  fi
done
